# API Configuration Guide

This document explains how to configure API endpoints for different environments in the Vehicle Service Contract application.

## Environment Variables

The application uses environment variables to configure API endpoints. Create a `.env` file in the `frontend` directory to override default settings.

### Available Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `VITE_API_BASE_URL` | `http://localhost:5000` | Base URL for the API server |
| `VITE_APP_TITLE` | `Vehicle Service Contract` | Application title |
| `VITE_DEBUG_MODE` | `false` | Enable debug mode |

### Example `.env` file

```bash
# Development
VITE_API_BASE_URL=http://localhost:5000
VITE_DEBUG_MODE=true

# Production
# VITE_API_BASE_URL=https://api.yourdomain.com
# VITE_DEBUG_MODE=false
```

## Configuration Files

### `src/config/environment.ts`
- Centralizes environment variable access
- Provides validation for required variables
- Offers environment-specific configurations

### `src/config/api.ts`
- Defines API endpoints and configuration
- Uses environment variables for base URL
- Provides helper functions for building URLs

## Usage Examples

### In API Services
```typescript
import { API_CONFIG, buildApiUrl } from '../config/api';

// Build full URL for an endpoint
const url = buildApiUrl(API_CONFIG.ENDPOINTS.VIN_DECODE);
// Result: http://localhost:5000/vin/decode (or your configured base URL)
```

### Environment Detection
```typescript
import { ENVIRONMENT } from '../config/environment';

if (ENVIRONMENT.IS_DEV) {
  console.log('Running in development mode');
}
```

## Deployment

### Development
- Uses default localhost URL
- Debug mode enabled by default
- Longer API timeouts for development

### Production
- Set `VITE_API_BASE_URL` to your production API URL
- Set `VITE_DEBUG_MODE=false`
- Shorter API timeouts for better performance

### Staging/Testing
- Create environment-specific `.env` files
- Use different base URLs for each environment
- Configure appropriate timeouts and retry settings

## Security Notes

- Never commit `.env` files to version control
- Use different API keys/URLs for different environments
- Validate environment variables on application startup
- Use HTTPS in production environments 