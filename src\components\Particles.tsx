import React, { useMemo } from 'react';
import { 
  PARTICLE_COUNT, 
  PARTICLE_DELAY_MAX, 
  PARTICLE_DURATION_MIN, 
  PARTICLE_DURATION_MAX 
} from '../constants/app';

const Particles: React.FC = () => {
  const particles = useMemo(() => 
    Array.from({ length: PARTICLE_COUNT }, (_, i) => ({
      id: i,
      delay: `${Math.random() * PARTICLE_DELAY_MAX}s`,
      duration: `${PARTICLE_DURATION_MIN + Math.random() * PARTICLE_DURATION_MAX}s`,
      x: `${Math.random() * 100}%`,
      y: `${Math.random() * 100}%`
    })), []
  );

  return (
    <div className="particles">
      {particles.map(({ id, delay, duration, x, y }) => (
        <div 
          key={id} 
          className="particle" 
          style={{
            '--delay': delay,
            '--duration': duration,
            '--x': x,
            '--y': y
          } as React.CSSProperties}
        />
      ))}
    </div>
  );
};

export default Particles; 