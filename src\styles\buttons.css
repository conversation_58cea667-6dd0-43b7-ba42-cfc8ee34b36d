/* Optimized <PERSON><PERSON> Styles using Design System */

/* Base Button Styles */
.shared-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  font-family: var(--font-family-primary);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  border: 2px solid transparent;
  min-width: 140px;
  position: relative;
  overflow: hidden;
}

/* Primary Button (Red) */
.shared-button.primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: var(--color-white);
  border-color: var(--color-primary);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.shared-button.primary:hover {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary-darker) 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
}

.shared-button.primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}

/* Secondary Button (Outlined) */
.shared-button.secondary {
  background: var(--color-white);
  color: var(--color-primary);
  border-color: var(--color-primary);
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.08);
}

.shared-button.secondary:hover {
  background: var(--color-primary-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
}

.shared-button.secondary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.08);
}

/* Tertiary Button (Gray) */
.shared-button.tertiary {
  background: var(--color-gray-50);
  color: var(--color-gray-800);
  border-color: var(--color-gray-300);
  box-shadow: var(--shadow-sm);
}

.shared-button.tertiary:hover {
  background: var(--color-gray-100);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.shared-button.tertiary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Disabled State */
.shared-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: var(--shadow-sm) !important;
}

/* Button with Icon */
.shared-button .button-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

/* Back Button Specific */
.back-button {
  margin-bottom: var(--spacing-6);
  font-size: var(--font-size-base);
  padding: var(--spacing-2) var(--spacing-6);
  min-width: auto;
  width: auto;
  max-width: fit-content;
}

/* Button Group */
.button-group {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  align-items: center;
}

/* Loading state */
.shared-button.loading {
  position: relative;
}

.shared-button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  right: var(--spacing-4);
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .shared-button {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-base);
    min-width: 120px;
  }
  
  .back-button {
    padding: var(--spacing-2) var(--spacing-5);
    font-size: var(--font-size-sm);
  }
  
  .button-group {
    flex-direction: column;
    gap: var(--spacing-2);
  }
  
  .button-group .shared-button {
    width: 100%;
  }
}

/* Lower Limit of Liability Active State */
.lower-limit-active {
  color: #dc2626 !important;
  border-color: #dc2626 !important;
}
.lower-limit-active:focus-visible {
  color: #dc2626 !important;
  border-color: #dc2626 !important;
  outline: none;
} 