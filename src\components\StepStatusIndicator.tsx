import React from 'react';

interface StepStatusIndicatorProps {
  stepId: string;
  status: 'pending' | 'in-progress' | 'completed' | 'locked';
  message?: string;
  requirements?: string[];
}

const StepStatusIndicator: React.FC<StepStatusIndicatorProps> = ({
  stepId,
  status,
  message,
  requirements = []
}) => {
  const getStatusColor = () => {
    switch (status) {
      case 'completed':
        return '#16a34a';
      case 'in-progress':
        return '#2563eb';
      case 'locked':
        return '#64748b';
      default:
        return '#e5e7eb';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return '✓';
      case 'in-progress':
        return '●';
      case 'locked':
        return '🔒';
      default:
        return '○';
    }
  };

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: 8,
      padding: '0.5rem 0.75rem',
      background: '#f8fafc',
      border: '1px solid #e5e7eb',
      borderRadius: 6,
      fontSize: 13,
      color: '#64748b'
    }}>
      <div style={{
        width: 16,
        height: 16,
        borderRadius: '50%',
        background: getStatusColor(),
        color: '#fff',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: 10,
        fontWeight: 'bold'
      }}>
        {getStatusIcon()}
      </div>
      <div>
        <div style={{ fontWeight: 500, color: '#334155' }}>
          {stepId === 'order' ? 'Build Contract' : 'Order Summary'}
        </div>
        {message && (
          <div style={{ fontSize: 12, marginTop: 2 }}>
            {message}
          </div>
        )}
        {requirements.length > 0 && (
          <div style={{ fontSize: 11, marginTop: 4, color: '#64748b' }}>
            <strong>Required:</strong> {requirements.join(', ')}
          </div>
        )}
      </div>
    </div>
  );
};

export default StepStatusIndicator; 