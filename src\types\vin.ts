// Consolidated VIN Types - Updated to match Gravity Forms field naming
export interface VinData {
  input_36: string;    // VIN (was: vin)
  input_4: string;     // Year (was: year)
  input_5: string;     // Make (was: make)
  input_6: string;     // Model (was: model)
  input_7: string;     // Trim (was: trim)
  input_138: string;   // Odometer (was: odometer)

  // Additional hidden fields for complete Gravity Forms compatibility
  input_11?: string;   // Body Style (e.g., "ASA44L/ASA42L/AVA44L")
  input_8?: string;    // Engine (e.g., "2AR-FE")
  input_9?: string;    // Cylinders (e.g., "6")
  input_10?: string;   // Drive Type (e.g., "4WD/4-Wheel Drive/4x4")
  input_37?: string;   // Vehicle Class (e.g., "Class 1")
}

export type AppStep = 1 | 2;

// API Response Types
export interface VinDecodeResponse {
  vin: string;
  make?: string;
  model?: string;
  year?: string;
  trim?: string;
  // Additional fields for hidden vehicle data
  bodyStyle?: string;
  engine?: string;
  cylinders?: string;
  driveType?: string;
  vehicleClass?: string;
  raw?: Array<{
    Variable: string;
    Value: string;
    [key: string]: any;
  }>;
  [key: string]: any;
}

// Form Validation Types - Updated to match Gravity Forms field naming
export interface VinFormErrors {
  input_36?: string;   // VIN errors (was: vin)
  input_138?: string;  // Odometer errors (was: odometer)
  general?: string;
}

// Loading States
export interface LoadingState {
  isDecoding: boolean;
  isSubmitting: boolean;
} 