/* Utility Classes using Design System */

/* Layout Utilities */
.container {
  max-width: 980px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.container-sm {
  max-width: 640px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.container-lg {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* Form Container */
.form-container {
  max-width: 980px;
  margin: var(--spacing-8) auto;
  font-family: var(--font-family-primary);
  padding: var(--spacing-10) var(--spacing-8);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
}

/* Card Styles */
.card {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  padding: var(--spacing-6);
}

.card-header {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-4);
  letter-spacing: -0.01em;
}

.card-divider {
  height: 1px;
  background: var(--color-gray-200);
  margin: var(--spacing-4) 0 var(--spacing-6) 0;
  border: none;
}

/* Section Styles */
.section {
  margin-bottom: var(--spacing-8);
}

.section-header {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-extrabold);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-3);
  letter-spacing: -0.01em;
}

.section-subheader {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-4);
}

/* Grid Utilities */
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-4);
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-4);
}

.grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-4);
}

/* Text Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

.text-gray-50 { color: var(--color-gray-50); }
.text-gray-100 { color: var(--color-gray-100); }
.text-gray-200 { color: var(--color-gray-200); }
.text-gray-300 { color: var(--color-gray-300); }
.text-gray-400 { color: var(--color-gray-400); }
.text-gray-500 { color: var(--color-gray-500); }
.text-gray-600 { color: var(--color-gray-600); }
.text-gray-700 { color: var(--color-gray-700); }
.text-gray-800 { color: var(--color-gray-800); }
.text-gray-900 { color: var(--color-gray-900); }

/* Background Utilities */
.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-success { background-color: var(--color-success); }
.bg-warning { background-color: var(--color-warning); }
.bg-error { background-color: var(--color-error); }

.bg-gray-50 { background-color: var(--color-gray-50); }
.bg-gray-100 { background-color: var(--color-gray-100); }
.bg-gray-200 { background-color: var(--color-gray-200); }
.bg-gray-300 { background-color: var(--color-gray-300); }
.bg-gray-400 { background-color: var(--color-gray-400); }
.bg-gray-500 { background-color: var(--color-gray-500); }
.bg-gray-600 { background-color: var(--color-gray-600); }
.bg-gray-700 { background-color: var(--color-gray-700); }
.bg-gray-800 { background-color: var(--color-gray-800); }
.bg-gray-900 { background-color: var(--color-gray-900); }

/* Border Utilities */
.border { border: 1px solid var(--color-gray-200); }
.border-0 { border: 0; }
.border-t { border-top: 1px solid var(--color-gray-200); }
.border-b { border-bottom: 1px solid var(--color-gray-200); }
.border-l { border-left: 1px solid var(--color-gray-200); }
.border-r { border-right: 1px solid var(--color-gray-200); }

.border-primary { border-color: var(--color-primary); }
.border-secondary { border-color: var(--color-secondary); }
.border-success { border-color: var(--color-success); }
.border-warning { border-color: var(--color-warning); }
.border-error { border-color: var(--color-error); }

/* Display Utilities */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }

/* Position Utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Responsive Utilities */
@media (max-width: 640px) {
  .sm\:hidden { display: none; }
  .sm\:block { display: block; }
  .sm\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .sm\:flex-col { flex-direction: column; }
  .sm\:text-sm { font-size: var(--font-size-sm); }
  .sm\:p-4 { padding: var(--spacing-4); }
  .sm\:m-4 { margin: var(--spacing-4); }
}

@media (max-width: 768px) {
  .md\:hidden { display: none; }
  .md\:block { display: block; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:flex-row { flex-direction: row; }
  .md\:text-base { font-size: var(--font-size-base); }
  .md\:p-6 { padding: var(--spacing-6); }
  .md\:m-6 { margin: var(--spacing-6); }
}

@media (max-width: 1024px) {
  .lg\:hidden { display: none; }
  .lg\:block { display: block; }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

/* Animation Utilities */
.transition { transition: all var(--transition-normal); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

.hover\:scale:hover { transform: scale(1.02); }
.hover\:shadow:hover { box-shadow: var(--shadow-lg); }
.hover\:bg-gray-50:hover { background-color: var(--color-gray-50); }

/* Focus Utilities */
.focus\:outline-none:focus { outline: none; }
.focus\:ring:focus { box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1); }
.focus\:border-primary:focus { border-color: var(--color-primary); } 