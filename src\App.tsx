import { useState, useCallback, useEffect } from "react";
import VinOdometerForm from './features/vinDecoder/VinOdometerForm'
import OrderFormPage from './features/orderForm/OrderFormPage'
import type { VinData, AppStep } from './types/vin'
import { APP_STEPS } from './constants/app'
import { validateEnvironment } from './config/environment'
import { StepProvider } from './contexts/StepContext'
import './App.css'

const initialVinData: VinData = {
  input_36: "",   // VIN
  input_4: "",    // Year
  input_5: "",    // Make
  input_6: "",    // Model
  input_7: "",    // Trim
  input_138: ""   // Odometer
};

function App() {
  // Validate environment configuration on app startup
  useEffect(() => {
    validateEnvironment();
  }, []);

  const [step, setStep] = useState<AppStep>(APP_STEPS.VIN_DECODER);
  const [vinData, setVinData] = useState<VinData>(initialVinData);
  const [vinDecodeSuccess, setVinDecodeSuccess] = useState(false);

  const handleNext = useCallback((data: VinData) => {
    setVinData(data);
    setVinDecodeSuccess(true);
    setStep(APP_STEPS.ORDER_FORM);
  }, []);

  const handleBack = useCallback(() => {
    setStep(APP_STEPS.VIN_DECODER);
    setVinDecodeSuccess(false);
  }, []);

  return (
    <StepProvider>
      <div className="app-background">
        
        {step === APP_STEPS.VIN_DECODER && (
          <VinOdometerForm
            onNext={handleNext}
          />
        )}
        {step === APP_STEPS.ORDER_FORM && (
          <OrderFormPage
            {...vinData}
            onBack={handleBack}
            vinDecodeSuccess={vinDecodeSuccess}
          />
        )}
      </div>
    </StepProvider>
  )
}

export default App
