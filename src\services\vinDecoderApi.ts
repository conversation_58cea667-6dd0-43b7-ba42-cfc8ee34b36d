import type { VinDecodeResponse } from '../types/vin';

// Simple error class for API errors
export class VinDecodeError extends Error {
  status?: number;
  code?: string;

  constructor(message: string, status?: number, code?: string) {
    super(message);
    this.name = 'VinDecodeError';
    this.status = status;
    this.code = code;
  }
}

// Main VIN decode function using NHTSA public API
export async function decodeVin(vin: string): Promise<VinDecodeResponse> {
  try {
    const response = await fetch(
      `https://vpic.nhtsa.dot.gov/api/vehicles/DecodeVin/${vin}?format=json`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new VinDecodeError('Failed to decode VIN', response.status);
    }

    const data = await response.json();
    // NHTSA returns Results array; map to VinDecodeResponse
    const results = data.Results || [];
    const getValue = (variable: string) => {
      const found = results.find((item: any) => item.Variable === variable);
      return found && found.Value && found.Value !== 'Not Applicable' && found.Value !== 'N/A' ? found.Value : '';
    };
    const vinResponse: VinDecodeResponse = {
      vin: vin.toUpperCase(),
      year: getValue('Model Year'),
      make: getValue('Make'),
      model: getValue('Model'),
      trim: getValue('Trim'),
      // Additional hidden fields for Gravity Forms compatibility
      bodyStyle: getValue('Body Class') || getValue('Vehicle Type') || getValue('Body Style'),
      engine: getValue('Engine Model') || getValue('Engine Configuration') || getValue('Engine'),
      cylinders: getValue('Engine Number of Cylinders') || getValue('Cylinders'),
      driveType: getValue('Drive Type') || getValue('Wheel Base Type') || getValue('Drivetrain'),
      vehicleClass: cleanVehicleClass(getValue('Vehicle Class') || getValue('GVWR Class') || getValue('Class') || getValue('Gross Vehicle Weight Rating From')),
      raw: results,
    };

    // Debug: Log available fields for development
    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
      const rawVehicleClass = getValue('Vehicle Class') || getValue('GVWR Class') || getValue('Class') || getValue('Gross Vehicle Weight Rating From');
      console.log('VIN Decode - Available NHTSA fields:', results.map((r: any) => r.Variable).sort());
      console.log('VIN Decode - Vehicle Class cleaning:', {
        raw: rawVehicleClass,
        cleaned: vinResponse.vehicleClass
      });
      console.log('VIN Decode - Mapped hidden fields:', {
        bodyStyle: vinResponse.bodyStyle,
        engine: vinResponse.engine,
        cylinders: vinResponse.cylinders,
        driveType: vinResponse.driveType,
        vehicleClass: vinResponse.vehicleClass
      });
    }
    return vinResponse;
  } catch (error) {
    if (error instanceof VinDecodeError) {
      throw error;
    }
    throw new VinDecodeError('Network error occurred', 0, 'NETWORK_ERROR');
  }
}

// Utility function to extract trim from raw data
export function extractTrimFromRaw(raw: VinDecodeResponse['raw']): string {
  if (!raw || !Array.isArray(raw)) return '';

  const trimObj = raw.find(function(item) {
    return item.Variable === 'Trim' && item.Value && item.Value !== 'N/A';
  });

  return trimObj ? trimObj.Value : '';
}

// Utility function to clean vehicle class value
export function cleanVehicleClass(vehicleClass: string): string {
  if (!vehicleClass) return '';

  // Extract "Class X" from strings like:
  // "Class 1C: 4,001 - 5,000 lb (1,814 - 2,268 kg)" → "Class 1"
  // "Class 2B: 6,001 - 7,000 lb" → "Class 2"
  // "Class 3A: 10,001 - 14,000 lb" → "Class 3"
  // "class 1c: 4,001 - 5,000 lb" → "Class 1" (case insensitive)
  const classMatch = vehicleClass.match(/Class\s+(\d+)[A-Z]?/i);
  if (classMatch) {
    return `Class ${classMatch[1]}`;
  }

  // If it already looks like "Class 1", return as is
  if (/^Class\s+\d+$/i.test(vehicleClass)) {
    return vehicleClass;
  }

  // For other formats, return the original value
  return vehicleClass;
}

/* Test cases for cleanVehicleClass function:
 * cleanVehicleClass("Class 1C: 4,001 - 5,000 lb (1,814 - 2,268 kg)") → "Class 1"
 * cleanVehicleClass("Class 2B: 6,001 - 7,000 lb") → "Class 2"
 * cleanVehicleClass("Class 1") → "Class 1"
 * cleanVehicleClass("class 1c: test") → "Class 1"
 * cleanVehicleClass("") → ""
 * cleanVehicleClass("Other Format") → "Other Format"
 */

// Validation helper
export function validateVinFormat(vin: string): boolean {
  const vinRegex = /^[A-HJ-NPR-Z0-9]{17}$/i;
  return vinRegex.test(vin) && vin !== '0'.repeat(17);
} 