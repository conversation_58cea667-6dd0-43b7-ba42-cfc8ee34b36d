import React from "react";
import sharedStyles from "../features/orderForm/OrderFormShared.module.css";
import styles from "../features/orderForm/OrderFormPage.module.css";

interface CheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  checked: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  label: React.ReactNode;
  className?: string;
  variant?: "neutral" | "blue";
}

const Checkbox: React.FC<CheckboxProps> = ({
  checked,
  onChange,
  label,
  className = "",
  variant = "neutral",
  ...props
}) => {
  if (variant === "blue") {
    // Use native input with surchargeCheckbox class for blue style
    return (
      <label style={{ display: "inline-flex", alignItems: "center", gap: 7, cursor: "pointer" }}>
        <input
          type="checkbox"
          checked={checked}
          onChange={onChange}
          className={`${styles.surchargeCheckbox} ${className}`}
          {...props}
        />
        <span style={{ color: "#334155", fontWeight: 500 }}>{label}</span>
      </label>
    );
  }
  // Default: neutral custom style
  return (
    <label style={{ display: "inline-flex", alignItems: "center", gap: 7, cursor: "pointer" }}>
      <input
        type="checkbox"
        checked={checked}
        onChange={onChange}
        className={`visually-hidden-checkbox ${className}`}
        {...props}
      />
      <span
        className={`${sharedStyles.customCheckbox} ${checked ? sharedStyles.checked : ""}`}
        aria-hidden="true"
      >
        {checked && (
          <svg viewBox="0 0 16 16" fill="none" stroke="currentColor" strokeWidth="2.2" strokeLinecap="round" strokeLinejoin="round">
            <polyline points="4,9 7,12 12,5" />
          </svg>
        )}
      </span>
      <span style={{ color: "#334155", fontWeight: 500 }}>{label}</span>
    </label>
  );
};

export default Checkbox; 