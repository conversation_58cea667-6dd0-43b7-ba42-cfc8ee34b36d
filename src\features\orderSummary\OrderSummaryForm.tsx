import React, { useState, useEffect } from "react";
import sharedStyles from "../orderForm/OrderFormShared.module.css";
import styles from "../orderForm/OrderFormPage.module.css";
import { motion, AnimatePresence } from "framer-motion";
import Checkbox from '../../components/Checkbox';
import SignaturePad from '../../components/SignaturePad';
import StepBar from '../../components/StepBar';
import { useStepContext } from '../../contexts/StepContext';
import { validateEmail, validateZipCode, validateNumericInput, formatPhoneNumber, formatCurrencyInput, removeCurrencyFormatting } from '../../utils/validation';


const inputStyle = {
  fontSize: window.innerWidth <= 800 ? 14 : 15,
  padding: window.innerWidth <= 800 ? '0.35rem 0.5rem' : '0.38rem 0.6rem',
  background: '#fff',
  border: '1px solid #e5e7eb',
  borderRadius: 5,
  height: window.innerWidth <= 800 ? 34 : 36
};

const containerStyle = {
  maxWidth: 980,
  margin: '2rem auto',
  fontFamily: `'Inter', 'Segoe UI', 'system-ui', 'Arial', sans-serif`,
  padding: window.innerWidth <= 800 ? '1rem 0.5rem' : '2.5rem 2rem',
  borderRadius: window.innerWidth <= 800 ? 0 : 16,
  boxShadow: '0 2px 12px rgba(0,0,0,0.07)',
  background: '#fff',
  border: '1px solid #e5e7eb',
  width: window.innerWidth <= 800 ? '100vw' : 'auto',
  boxSizing: 'border-box' as const
};

const labelStyle = {
  color: '#334155',
  fontWeight: 500,
  fontSize: window.innerWidth <= 800 ? 11 : 13,
  marginBottom: 2,
  background: 'transparent',
  lineHeight: window.innerWidth <= 800 ? 1.2 : 1.4,
};

function AllLienholdersForm() {
  return (
    <form className="all-lienholders-form">
      <div className="all-lienholders-grid">
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <label className="shared-input-label" style={labelStyle}>State</label>
          <select className="shared-input" style={inputStyle}>
            <option>UT</option>
            {/* Add more states as needed */}
          </select>
        </div>
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <label className="shared-input-label" style={labelStyle}>Select Lienholder</label>
          <select className="shared-input" style={inputStyle}>
            <option value="">Select...</option>
            {/* Add lienholder options as needed */}
          </select>
        </div>
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <label className="shared-input-label" style={labelStyle}>All Lienholder Email</label>
          <input type="email" className="shared-input" style={inputStyle} />
        </div>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
        <label className="shared-input-label" style={labelStyle}>Selected Lienholder</label>
        <input type="text" className="shared-input" style={inputStyle} />
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
        <label className="shared-input-label" style={labelStyle}>Selected Lienholder Email</label>
        <input type="email" className="shared-input" style={inputStyle} />
      </div>
      <style>{`
        .all-lienholders-form {
          background: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 10px;
          padding: 1.3rem 1.5rem 1.2rem 1.5rem;
          margin-bottom: 0.5rem;
          display: flex;
          flex-direction: column;
          gap: 18px;
        }
        .all-lienholders-grid {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          gap: 18px;
        }
        @media (max-width: 900px) {
          .all-lienholders-grid {
            grid-template-columns: 1fr;
            gap: 12px;
          }
        }
      `}</style>
    </form>
  );
}

const consumerDisclosureText = `Consumer Disclosure Regarding Conducting Business Electronically, Signing Documents Electronically, and Receiving Electronic Notices and Disclosures

Please read the information below, carefully, as it concerns your rights. eSignatures are an efficient way to execute an agreement with the same legal force and effect of a handwritten or "wet ink" signature. By signing this document you are agreeing that you have reviewed this Consumer Disclosure and consent and intend to transact business electronically; to use electronic signatures instead of wet ink signatures and paper documents, and to receive notices and disclosures electronically.

You are not required to sign documents electronically or to receive notices and disclosures electronically. If you prefer not to transact business electronically, you may request paper copies from the "sending party" and withdraw your consent at any time, as described below.

Scope of Consent
By utilizing this Service, you agree to receive electronic signature documents with all related and identified documents, notices, and disclosures provided during your relationship with the "sending party." You may withdraw your consent, at any time, by following the procedures outlined below.

Paper Copies
You are not required to sign documents electronically, or receive notices or disclosures electronically, and may request paper copies of documents or disclosures, if you prefer. You also have the ability to download and print any signed or unsigned documents sent to you through the electronic signature service. We may also email you a copy of all documents you sign using the electronic signature service. If you wish to receive paper copies instead of electronic documents you may close this web browser and request paper copies from the "sending party" by following the procedures outlined below. The "sending party" may apply a charge for additional expenses incurred by printing and mailing paper copies.

Withdrawal of Consent
You may withdraw your consent to receive electronic documents, notices or disclosures at any time. In order to withdraw consent you must notify the "sending party" that you wish to withdraw your consent to transact business electronically and to provide your future documents, notices, and disclosures in paper format. If at any time, after withdrawing your consent you choose to use our electronic signature system your use of this Service will, once again, evidence your consent to receive documents, notices, and disclosures, electronically. You may withdraw your consent to receive electronic notices and disclosures or execute an electronic signature by following the procedures described below.

Withdrawing your consent, requesting a paper copy, or updating your contact information
You always have the ability to download and print any documents sent to you through our electronic signature system. To withdraw your consent to conduct business electronically, sign documents electronically, and receive documents, notices, or disclosures electronically, please contact the "sending party" directly; by telephone, by email (sent to the "sending party" with any of the topics outlined below stated in the subject line of your email) or by postal mail to their mailing address specified to receive such notices.

"Withdrawal of Consent To Transact Business Electronically" To allow the "sending party" to identify and facilitate your withdrawal of consent to transact business electronically, please provide your name, email address, the date on which you are withdrawing your consent, your telephone number and mailing address.

"Requesting A Paper Copy" To allow the "sending party" to identify you to provide a paper copy of the document requiring your signature, the notice, or disclosure, please provide the sending party with your name, email address, mailing address, telephone number, and name of the document of which you are requesting a paper copy .

"Update Your Contact Information" To allow the "sending party" to identify you in order to update your contact information, please provide them with your name, email address, mailing address, and telephone number.

The "sending party" will inform you of any fees related to costs for printing and mailing paper copies or your withdrawal consent to transact business electronically.`;

const OrderSummaryForm: React.FC<{ onBack: () => void }> = ({ onBack }) => {
  const [form, setForm] = useState({
    input_239: '',    // Dealer Name (was: dealership)
    input_240: '',    // Dealer Phone (was: dealerPhone)
    input_241: '',    // Dealer Fax (was: dealerFax)
    input_304: '',    // Dealer Website (was: dealerSite)
    input_324: '',    // Dealer Email (was: serviceManagerEmail)
    input_367: 'Yes', // Repair Facility (was: repairFacility)
    input_243_3: '',  // Salesperson First (was: salespersonFirst)
    input_243_6: '',  // Salesperson Last (was: salespersonLast)
    input_295: '',    // Salesperson Email (was: salespersonEmail)
    input_261: '',    // Address (was: address)
    input_262: '',    // City (was: city)
    input_263: '',    // State (was: state)
    input_264: '',    // ZIP (was: zip)
  });
  const [lienholderOption, setLienholderOption] = useState('all');
  const [sendLienholderEmail, setSendLienholderEmail] = useState(false);
  const [editMarkupChecked, setEditMarkupChecked] = useState(false);
  const [markupAmount, setMarkupAmount] = useState('');
  const [legalConsentChecked, setLegalConsentChecked] = useState(false);
  const [buyerSignature, setBuyerSignature] = useState('');
  const [dealerSignature, setDealerSignature] = useState('');

  // Initialize step context
  const {
    currentStepId,
    goToStep,
    markStepCompleted,
    getCurrentSteps,
    getPreviousStep
  } = useStepContext();

  // Currency field handlers for markup
  const handleMarkupChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = validateNumericInput(e.target.value, true);
    setMarkupAmount(value);
  };

  const handleMarkupBlur = () => {
    if (markupAmount) {
      const formatted = formatCurrencyInput(markupAmount);
      setMarkupAmount(formatted);
    }
  };

  const handleMarkupFocus = () => {
    const numericValue = removeCurrencyFormatting(markupAmount);
    setMarkupAmount(numericValue);
  };

  // Check if form has required data
  const hasRequiredData = () => {
    const requiredFields = ['dealership', 'salespersonFirst', 'salespersonLast'];
    return requiredFields.every(field => form[field as keyof typeof form] && form[field as keyof typeof form].trim() !== '');
  };

  // Check if signatures are completed
  const hasSignatures = () => {
    return buyerSignature.trim() !== '' && dealerSignature.trim() !== '';
  };

  // Check if legal consent is given
  const hasLegalConsent = () => {
    return legalConsentChecked;
  };

  // Update step completion based on form progress
  useEffect(() => {
    if (hasRequiredData() && hasSignatures() && hasLegalConsent()) {
      markStepCompleted('summary', true);
    }
  }, [form, buyerSignature, dealerSignature, legalConsentChecked, markStepCompleted]);

  // Custom step navigation that maintains completion status
  const handleStepNavigation = (stepId: string) => {
    if (stepId === 'summary') {
      // Already on summary, do nothing
      return;
    }

    // For any other step, navigate back through the parent
    goToStep(stepId);
    onBack(); // This will trigger the parent to handle the navigation
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <div style={containerStyle} className="order-summary-form-container-fix">
      <StepBar
        steps={getCurrentSteps()}
        currentStep={currentStepId}
        onStepClick={handleStepNavigation}
        allowNavigation={true}
      />
      {/* Form Title */}
      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <h1 style={{
          fontSize: '2rem',
          fontWeight: 700,
          color: '#334155',
          margin: 0,
          letterSpacing: '-0.02em'
        }}>
          Order Summary
        </h1>
        <div style={{
          height: 2,
          background: 'linear-gradient(90deg, #2563eb, #dc2626)',
          width: 60,
          margin: '0.5rem auto 0 auto',
          borderRadius: 1
        }} />
      </div>

      {/* Dealer Info Section */}
      <div className={sharedStyles.orderSectionCard}>
        <h2 className={sharedStyles.orderSectionHeaderModern}>Dealer Info</h2>
        <div className={sharedStyles.orderSectionDivider} />
        <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
          <div className={sharedStyles.orderGrid4} style={{ gap: 10 }}>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Dealership</label>
              <input name="input_239" value={form.input_239} onChange={handleChange} className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Dealer Phone</label>
              <input name="input_240" value={form.input_240} onChange={handleChange} className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Dealer Fax</label>
              <input name="input_241" value={form.input_241} onChange={handleChange} className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Dealer Site</label>
              <input name="input_304" value={form.input_304} onChange={handleChange} className="shared-input" style={inputStyle} />
            </div>
          </div>
          <div className={sharedStyles.orderGrid2} style={{ gap: 10 }}>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Service Manager Email</label>
              <input name="input_324" value={form.input_324} onChange={handleChange} className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Repair Facility?</label>
              <select name="input_367" value={form.input_367} onChange={handleChange} className="shared-input" style={inputStyle}>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
              </select>
            </div>
          </div>
          <div className={sharedStyles.orderGrid3} style={{ gap: 10 }}>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Salesperson</label>
              <div style={{ display: 'flex', gap: 6 }}>
                <input name="input_243_3" value={form.input_243_3} onChange={handleChange} placeholder="First" className="shared-input" style={{ ...inputStyle, flex: 1 }} />
                <input name="input_243_6" value={form.input_243_6} onChange={handleChange} placeholder="Last" className="shared-input" style={{ ...inputStyle, flex: 1 }} />
              </div>
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Salesperson Email</label>
              <input name="input_295" value={form.input_295} onChange={handleChange} className="shared-input" style={inputStyle} />
            </div>
          </div>
          <div className={sharedStyles.orderGridAddress} style={{ gap: 10 }}>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Street Address</label>
              <input name="input_261" value={form.input_261} onChange={handleChange} className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>City</label>
              <input name="input_262" value={form.input_262} onChange={handleChange} className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>State</label>
              <input name="input_263" value={form.input_263} onChange={handleChange} className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>ZIP Code</label>
              <input name="input_264" value={form.input_264} onChange={handleChange} className="shared-input" style={inputStyle} />
            </div>
          </div>
        </div>
      </div>

      {/* Buyer Info Section */}
      <div className={sharedStyles.orderSectionCard}>
        <h2 className={sharedStyles.orderSectionHeaderModern}>Buyer Info</h2>
        <div className={sharedStyles.orderSectionDivider} />
        <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
          <div className={sharedStyles.orderGrid2} style={{ gap: 10 }}>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Buyer's Name</label>
              <div style={{ display: 'flex', gap: 6 }}>
                <input name="buyerFirst" placeholder="First" className="shared-input" style={{ ...inputStyle, flex: 1 }} />
                <input name="buyerLast" placeholder="Last" className="shared-input" style={{ ...inputStyle, flex: 1 }} />
              </div>
            </div>
          </div>
          <div className={sharedStyles.orderGrid2} style={{ gap: 10 }}>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Buyer's Phone</label>
              <input name="buyerPhone" className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Buyer's Email</label>
              <input name="buyerEmail" className="shared-input" style={inputStyle} />
            </div>
          </div>
          <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
            <label className="shared-input-label" style={labelStyle}>Buyer's Mailing Address</label>
            <input name="buyerStreet" placeholder="Street Address" className="shared-input" style={inputStyle} />
          </div>
          <div className={sharedStyles.orderGrid2} style={{ gap: 10 }}>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <input name="buyerCity" placeholder="City" className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <input name="buyerState" placeholder="State" className="shared-input" style={inputStyle} />
            </div>
          </div>
          <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
            <input name="buyerZip" placeholder="ZIP Code" className="shared-input" style={inputStyle} />
          </div>
        </div>
      </div>

      {/* Contract Info Section */}
      <div className={sharedStyles.orderSectionCard}>
        <h2 className={sharedStyles.orderSectionHeaderModern}>Contract Info</h2>
        <div className={sharedStyles.orderSectionDivider} />
        <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: window.innerWidth <= 800 ? 'repeat(2, 1fr)' : '1fr 1fr 1fr 1fr',
            gap: 10,
            marginBottom: 8
          }} className="contract-info-grid">
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Contract #</label>
              <input name="contractNumber" className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Selected Contract</label>
              <input name="selectedContract" className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Contract Term in Months</label>
              <input name="contractTerm" className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Deductible</label>
              <input name="deductible" className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Final Contract Price</label>
              <input name="finalContractPrice" className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Waiting Period (if applicable)</label>
              <input name="waitingPeriod" className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Contract Effective Miles</label>
              <input name="contractEffectiveMiles" className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Contract Expiration Miles</label>
              <input name="contractExpirationMiles" className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Vehicle Purchase Date</label>
              <input name="vehiclePurchaseDate" className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>VSC Purchase Date</label>
              <input name="vscPurchaseDate" className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Contract Effective Date</label>
              <input name="contractEffectiveDate" className="shared-input" style={inputStyle} />
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Contract Expiration Date</label>
              <input name="contractExpirationDate" className="shared-input" style={inputStyle} />
            </div>
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: 10, marginBottom: 0 }}>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Vehicle Purchase Price <span style={{ color: '#b91c1c', fontWeight: 600 }}>(Required)</span></label>
              <input name="vehiclePurchasePrice" className="shared-input" style={inputStyle} />
              <div className={sharedStyles.orderHelperText}>Used to determine Limit of Liability</div>
            </div>
            <div className={sharedStyles.orderInputGroup} style={{ gap: 1 }}>
              <label className="shared-input-label" style={labelStyle}>Limit of Liability</label>
              <input name="limitOfLiability" className="shared-input" style={inputStyle} />
            </div>
          </div>
          <div style={{ marginTop: 8, display: 'flex', alignItems: 'center', gap: 7 }}>
            <Checkbox
              id="editMarkup"
              name="editMarkup"
              checked={editMarkupChecked}
              onChange={e => setEditMarkupChecked(e.target.checked)}
              label="Edit Markup"
              variant="blue"
            />
          </div>

          {/* Edit Markup Form - Show when checkbox is checked */}
          {editMarkupChecked && (
            <div style={{
              marginTop: 16,
              background: '#f9fafb',
              border: '1px solid #e5e7eb',
              borderRadius: 8,
              padding: '1.2rem 1.4rem'
            }}>
              <div style={{ fontWeight: 600, fontSize: 14, marginBottom: 12 }}>
                Current Markup is $178.00
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 4, marginBottom: 12 }}>
                <input
                  type="text"
                  className="shared-input"
                  style={inputStyle}
                  placeholder="Enter new markup amount (e.g., $150.00)"
                  value={markupAmount}
                  onChange={handleMarkupChange}
                  onBlur={handleMarkupBlur}
                  onFocus={handleMarkupFocus}
                />
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 4, marginBottom: 12 }}>
                <label className="shared-input-label" style={labelStyle}>Selected Lienholder</label>
                <input
                  type="text"
                  className="shared-input"
                  style={inputStyle}
                />
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                <label className="shared-input-label" style={labelStyle}>Selected Lienholder Email</label>
                <input
                  type="email"
                  className="shared-input"
                  style={inputStyle}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Lienholder Info Section */}
      <div className={sharedStyles.orderSectionCard} style={{
        padding: window.innerWidth <= 800 ? '1rem 0.5rem' : '0.7rem 0.7rem'
      }}>
        <h2 className={sharedStyles.orderSectionHeaderModern} style={{
          marginBottom: 4,
          fontSize: window.innerWidth <= 800 ? '0.9rem' : '1rem'
        }}>
          Lienholder Info
        </h2>
        <div className={sharedStyles.orderSectionDivider} style={{ margin: '4px 0 8px 0', height: 1 }} />
        <div style={{ display: 'flex', flexDirection: 'column', gap: 6 }}>
          <div style={{ fontWeight: 700, fontSize: window.innerWidth <= 800 ? 12 : 13, marginBottom: 2 }}>
            Lienholder options
          </div>
          <div className={styles.orderTabs} role="tablist" style={{
            flexDirection: 'row',
            gap: window.innerWidth <= 800 ? '0.2rem' : '0',
            padding: window.innerWidth <= 800 ? '0.2rem' : '0',
            overflowX: window.innerWidth <= 800 ? 'auto' : 'visible',
            whiteSpace: window.innerWidth <= 800 ? 'nowrap' : 'normal'
          }}>
            {[
              { value: 'all', label: 'Show all Lienholders', short: 'All' },
              { value: 'existing', label: 'Show my existing Lienholders', short: 'Existing' },
              { value: 'add', label: 'Add a lienholder', short: 'Add New' },
            ].map(opt => (
              <button
                key={opt.value}
                type="button"
                className={`${styles.orderTab} ${lienholderOption === opt.value ? styles.active : ''}`}
                aria-selected={lienholderOption === opt.value}
                role="tab"
                tabIndex={lienholderOption === opt.value ? 0 : -1}
                onClick={() => setLienholderOption(opt.value)}
                style={{
                  fontSize: window.innerWidth <= 800 ? '11px' : '1.08rem',
                  padding: window.innerWidth <= 800 ? '0.3rem 0.6rem' : '0.7rem 1.6rem',
                  fontWeight: lienholderOption === opt.value ? 700 : 500,
                  whiteSpace: 'nowrap',
                  flexShrink: 0,
                  flex: window.innerWidth <= 800 ? '1' : 'auto',
                  minHeight: window.innerWidth <= 800 ? '10px' : 'auto',
                  height: window.innerWidth <= 800 ? '10px' : 'auto'
                }}
              >
                {window.innerWidth <= 800 ? opt.short : opt.label}
              </button>
            ))}
          </div>
          {/* Tab content area below options with framer-motion transition */}
          <AnimatePresence mode="wait">
            <motion.div
              key={lienholderOption}
              initial={{ opacity: 0, x: 64 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -64 }}
              transition={{ duration: 0.32, ease: [0.4, 0, 0.2, 1] }}
              style={{ background: '#f8fafc', border: '1px solid #e5e7eb', borderTop: 'none', borderRadius: '0 0 8px 8px', padding: '1.1rem 1.2rem', marginBottom: 10, marginTop: -2, minHeight: 48 }}
            >
              {lienholderOption === 'all' && (
                <AllLienholdersForm />
              )}
              {lienholderOption === 'existing' && (
                <AllLienholdersForm />
              )}
              {lienholderOption === 'add' && (
                <div style={{ background: '#f8fafc', border: '1px solid #e5e7eb', borderRadius: 8, padding: '1.2rem 1.4rem', boxShadow: '0 1px 4px rgba(0,0,0,0.03)', maxWidth: 600, margin: '0 auto' }}>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 14 }}>
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 10 }}>
                      <div>
                        <label className="shared-input-label" style={labelStyle}>Name</label>
                        <input className="shared-input" style={inputStyle} />
                      </div>
                      <div>
                        <label className="shared-input-label" style={labelStyle}>Phone</label>
                        <input
                          type="tel"
                          className="shared-input"
                          style={inputStyle}
                          onChange={(e) => {
                            const formatted = formatPhoneNumber(e.target.value);
                            e.target.value = formatted;
                          }}
                          placeholder="(*************"
                          maxLength={14}
                        />
                      </div>
                    </div>
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 10 }}>
                      <div>
                        <label className="shared-input-label" style={labelStyle}>Email</label>
                        <input
                          type="email"
                          className="shared-input"
                          style={inputStyle}
                          placeholder="<EMAIL>"
                          onBlur={(e) => {
                            const error = validateEmail(e.target.value);
                            if (error && e.target.value) {
                              e.target.style.borderColor = '#dc2626';
                            } else {
                              e.target.style.borderColor = '#e5e7eb';
                            }
                          }}
                        />
                      </div>
                      <div>
                        <label className="shared-input-label" style={labelStyle}>Street Address</label>
                        <input className="shared-input" style={inputStyle} />
                      </div>
                    </div>
                    <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr 1fr', gap: 10 }}>
                      <div>
                        <label className="shared-input-label" style={labelStyle}>City</label>
                        <input className="shared-input" style={inputStyle} />
                      </div>
                      <div>
                        <label className="shared-input-label" style={labelStyle}>State</label>
                        <input className="shared-input" style={inputStyle} />
                      </div>
                      <div>
                        <label className="shared-input-label" style={labelStyle}>ZIP Code</label>
                        <input
                          type="text"
                          className="shared-input"
                          style={inputStyle}
                          onChange={(e) => {
                            const value = validateNumericInput(e.target.value, false);
                            e.target.value = value;
                          }}
                          onBlur={(e) => {
                            const error = validateZipCode(e.target.value);
                            if (error && e.target.value) {
                              e.target.style.borderColor = '#dc2626';
                            } else {
                              e.target.style.borderColor = '#e5e7eb';
                            }
                          }}
                          placeholder="12345"
                          maxLength={10}
                        />
                      </div>
                    </div>

                    {/* Selected Lienholder Section */}
                    <div style={{
                      borderTop: '1px solid #e5e7eb',
                      paddingTop: '1rem',
                      marginTop: '0.5rem',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 14
                    }}>
                      <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                        <label className="shared-input-label" style={labelStyle}>Selected Lienholder</label>
                        <input
                          type="text"
                          className="shared-input"
                          style={inputStyle}
                          placeholder="Enter selected lienholder name"
                        />
                      </div>
                      <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                        <label className="shared-input-label" style={labelStyle}>Selected Lienholder Email</label>
                        <input
                          type="email"
                          className="shared-input"
                          style={inputStyle}
                          placeholder="Enter selected lienholder email"
                          onBlur={(e) => {
                            const error = validateEmail(e.target.value);
                            if (error && e.target.value) {
                              e.target.style.borderColor = '#dc2626';
                            } else {
                              e.target.style.borderColor = '#e5e7eb';
                            }
                          }}
                        />
                      </div>
                    </div>

                    <button type="submit" className="shared-button primary" style={{ marginTop: 8, alignSelf: 'flex-end' }}>Add Lienholder</button>
                  </div>
                </div>
              )}
            </motion.div>
          </AnimatePresence>
          <div style={{ marginTop: 8 }}>
            <Checkbox
              checked={sendLienholderEmail}
              onChange={e => setSendLienholderEmail(e.target.checked)}
              label="Send Lienholder Email"
              variant="blue"
            />
          </div>
        </div>
      </div>

      {/* Legal Consent Section */}
      <div className={sharedStyles.orderSectionCard}>
        <h2 className={sharedStyles.orderSectionHeaderModern}>
          Legal Consent <span style={{ color: '#b91c1c', fontWeight: 600, fontSize: 13 }}>(Required)</span>
        </h2>
        <div className={sharedStyles.orderSectionDivider} />
        <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
          <div style={{ marginBottom: 12 }}>
            <Checkbox
              name="legalConsent"
              checked={legalConsentChecked}
              onChange={e => setLegalConsentChecked(e.target.checked)}
              label="I agree to the terms and conditions."
              variant="blue"
            />
          </div>
          <div style={{ background: '#f9fafb', border: '1px solid #e5e7eb', borderRadius: 7, padding: '1rem 1.2rem', marginBottom: 18, fontSize: 14, color: '#334155' }}>
            <textarea
              name="paperCopies"
              value={consumerDisclosureText}
              disabled
              style={{
                width: '100%',
                minHeight: 120,
                border: 'none',
                background: 'transparent',
                fontSize: 14,
                color: '#334155',
                fontFamily: 'inherit',
                outline: 'none',
                cursor: 'default'
              }}
              rows={12}
            />
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 18, marginBottom: 18 }}>
            <div>
              <div style={{ fontWeight: 700, fontSize: window.innerWidth <= 800 ? 13 : 15, marginBottom: 6 }}>
                {window.innerWidth <= 800 ? "Buyer's Signature" : "Buyer's Signature"}
              </div>
              <SignaturePad
                value={buyerSignature}
                onChange={setBuyerSignature}
                placeholder="Buyer's Signature"
                required={true}
              />
            </div>
            <div>
              <div style={{ fontWeight: 700, fontSize: window.innerWidth <= 800 ? 13 : 15, marginBottom: 6 }}>
                {window.innerWidth <= 800 ? "Dealer Signature" : "Dealer Representative Signature"}
              </div>
              <SignaturePad
                value={dealerSignature}
                onChange={setDealerSignature}
                placeholder="Dealer Representative Signature"
                required={true}
              />
            </div>
          </div>
          <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', gap: 18, marginTop: 8 }}>
            <span style={{ color: '#64748b', fontSize: window.innerWidth <= 800 ? 13 : 15 }}>Not interested in Coverage for your Vehicle?</span>
            <button
              type="button"
              className={sharedStyles.orderButtonDangerModern}
              style={{
                fontSize: window.innerWidth <= 800 ? 12 : undefined,
                padding: window.innerWidth <= 800 ? '6px 12px' : undefined
              }}
            >
              Decline Coverage
            </button>
          </div>
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 10, marginTop: 10 }}>
            <button type="button" onClick={onBack} className={sharedStyles.orderButtonPrimaryModern} style={{
              background: '#f3f4f6',
              color: '#334155',
              fontWeight: 600,
              fontSize: window.innerWidth <= 800 ? 14 : undefined,
              padding: window.innerWidth <= 800 ? '8px 16px' : undefined
            }}>Back</button>
            <button type="submit" className="shared-button primary" style={{
              fontSize: window.innerWidth <= 800 ? 14 : undefined,
              padding: window.innerWidth <= 800 ? '8px 16px' : undefined
            }}>Submit</button>
          </div>
        </div>
      </div>
      <style>{`
        .dealer-info-grid {
          display: grid !important;
          grid-template-columns: ${window.innerWidth <= 800 ? 'repeat(2, 1fr)' : 'repeat(auto-fit, minmax(200px, 1fr))'} !important;
          gap: ${window.innerWidth <= 800 ? '8px' : '12px'} !important;
        }
        .contract-info-grid {
          display: grid !important;
          grid-template-columns: ${window.innerWidth <= 800 ? 'repeat(2, 1fr)' : '1fr 1fr 1fr 1fr'} !important;
          gap: 10px !important;
        }
      `}</style>
    </div>
  );
};

export default OrderSummaryForm;
























