.surchargesCard {
  background: #f6f8fa;
  border-radius: 8px;
  box-shadow: none;
  border: none;
  padding: 1.5rem 2.5rem;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  gap: 25px;
  align-items: flex-start;
}

@media (max-width: 800px) {
  .surchargesCard {
    flex-direction: column !important;
    padding: 1rem !important;
    gap: 1.5rem !important;
  }

  .surchargesColumn {
    width: 100% !important;
  }

  .surchargesTitle {
    font-size: 1rem !important;
    margin-bottom: 0.75rem !important;
  }

  .surchargeOption,
  .deductibleOption {
    padding: 0.5rem 0 !important;
    font-size: 0.95rem !important;
  }

  .surchargeCheckbox,
  .deductibleRadio {
    width: 18px !important;
    height: 18px !important;
    margin-right: 6px !important;
  }
}

.surchargesColumn {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.surchargesTitle {
  font-weight: 700;
  font-size: 1.1rem;
  color: #334155;
  margin-bottom: 10px;
  letter-spacing: -0.01em;
}

.surchargeOption,
.deductibleOption {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
  padding: 7px 0;
  cursor: pointer;
  border-radius: 6px;
  transition: background 0.15s;
}

.surchargeOption:hover,
.deductibleOption:hover {
  background: #f1f5f9;
}

/* Custom Checkbox */
.surchargeCheckbox,
.deductibleRadio {
  position: relative;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  cursor: pointer;
  appearance: none;
  background: #fff;
  border: 1.5px solid #cbd5e1;
  border-radius: 5px;
  transition: border 0.15s, box-shadow 0.15s;
  box-shadow: none;
  display: inline-block;
  vertical-align: middle;
}

.surchargeCheckbox:checked {
  border-color: #2563eb;
  background: #2563eb;
}

.surchargeCheckbox:checked::after {
  content: '';
  display: block;
  position: absolute;
  left: 5px;
  top: 2px;
  width: 6px;
  height: 12px;
  border: solid #fff;
  border-width: 0 2.5px 2.5px 0;
  border-radius: 1px;
  transform: rotate(45deg);
}

.deductibleRadio {
  border-radius: 50%;
}

.deductibleRadio:checked {
  border-color: #2563eb;
  background: #fff;
}

.deductibleRadio:checked::after {
  content: '';
  display: block;
  position: absolute;
  left: 5px;
  top: 5px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #2563eb;
}

.dividerRow {
  display: flex;
  align-items: center;
  margin: 28px 0 12px 0;
}

.dividerLine {
  flex: 1;
  height: 1px;
  background: #e5e7eb;
}

.dividerTitle {
  padding: 0 18px;
  font-weight: 700;
  font-size: 1.1rem;
  color: #334155;
  white-space: nowrap;
}

.paymentCalculatorCard {
  background: #f6f8fa;
  border-radius: 8px;
  box-shadow: none;
  border: none;
  padding: 1.5rem 2.5rem;
  margin: 0 0 18px 0;
  width: 100%;
  box-sizing: border-box;
}

@media (max-width: 800px) {
  .paymentCalculatorCard {
    padding: 1rem !important;
    max-width: 100%;
  }

  .paymentInputRow {
    flex-direction: column !important;
    gap: 12px !important;
  }

  .paymentInputLabel {
    font-size: 0.9rem !important;
    margin-bottom: 2px !important;
  }

  .paymentInput {
    font-size: 0.95rem !important;
    padding: 0.45rem 0.65rem !important;
  }

  .paymentShortInput {
    max-width: 100% !important;
  }

  .estimatedPaymentRow {
    font-size: 0.95rem !important;
    padding: 0.5rem 0.75rem !important;
    margin-top: 16px !important;
  }
}

.sectionDivider {
  width: 100%;
  height: 1px;
  background: #e5e7eb;
  margin: 32px 0 18px 0;
  border: none;
}

.paymentCalculatorTitle {
  font-weight: 600;
  font-size: 1.05rem;
  color: #334155;
  margin-bottom: 4px;
  letter-spacing: -0.01em;
}

.paymentInputGroup {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.paymentInputLabel {
  font-size: 0.97rem;
  color: #475569;
  font-weight: 500;
  margin-bottom: 1px;
}

.paymentInput {
  font-size: 0.98rem;
  padding: 0.38rem 0.6rem;
  border: 1px solid #e5e7eb;
  border-radius: 5px;
  background: #f8fafc;
  outline: none;
  transition: border 0.13s;
  box-shadow: none;
}

.paymentInput:focus {
  border: 1.2px solid #2563eb;
  background: #fff;
}

.paymentInputRow {
  display: flex;
  flex-direction: row;
  gap: 18px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

@media (max-width: 600px) {
  .paymentInputRow {
    flex-direction: column;
    gap: 8px;
  }
}

.estimatedPaymentRow {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 500;
  color: #2563eb;
  background: #f6f8fa;
  border-radius: 5px;
  padding: 0.38rem 0.6rem;
  margin-top: 14px;
  border: 1px solid #e5e7eb;
}

.paymentCheckboxGroup {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 6px;
}

.paymentCheckboxLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.98rem;
  cursor: pointer;
  border-radius: 5px;
  padding: 3px 0;
  transition: background 0.13s;
}

.paymentCheckboxLabel:hover {
  background: #f3f6fa;
}

.paymentCheckbox {
  accent-color: #2563eb;
  width: 16px;
  height: 16px;
  margin-right: 4px;
  cursor: pointer;
}

.formContainer {
  width: 70vw;
  max-width: 1600px;
  margin: 2.5rem auto;
  padding: 3rem 3rem;
  border-radius: 16px;
  background: #fff;
  border: 1px solid #e5e7eb;
  font-family: 'Inter', 'Segoe UI', 'system-ui', 'Arial', sans-serif;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  gap: 0;
  box-sizing: border-box;
}

@media (max-width: 768px) {
  .formContainer {
    width: 100vw;
    max-width: 100vw;
    margin: 0;
    padding: 1rem 0.5rem;
    border-radius: 0;
    box-sizing: border-box;
  }
}

.infoCard {
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid #e5e7eb;
  border-radius: 14px;
  background: #f9fafb;
  padding: 3rem 3rem 3.2rem 3rem;
  margin-bottom: 0;
  box-shadow: none;
}

.sectionTitle {
  font-weight: 700;
  font-size: 1.13rem;
  color: #1e293b;
  margin-bottom: 10px;
  letter-spacing: -0.01em;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 12px;
}

.inputLabel {
  font-size: 0.98rem;
  color: #475569;
  font-weight: 500;
  margin-bottom: 2px;
}

.inputField {
  font-size: 1rem;
  padding: 1.25rem 0.7rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f8fafc;
  outline: none;
  transition: border 0.15s, background 0.15s;
  box-shadow: none;
  color: #334155;
  width: 100%;
  appearance: none;
}

.inputField:focus {
  border: 1.2px solid #2563eb;
  background: #fff;
}

.inputField[readonly],
.inputField[disabled] {
  background: #f3f4f6;
  color: rgb(100 116 139);
  border: 1px solid #e5e7eb;
  cursor: not-allowed;
}

/* Remove number input spinners/arrows for all browsers */
.inputField[type=number] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

.inputField[type=number]::-webkit-outer-spin-button,
.inputField[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.inputWithIcon {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.inputField.withIcon,
.shared-input.withIcon {
  padding-right: 2.2rem;
  /* Add space for the icon */
}

.inputField.withIcon {
  padding-right: 2.2rem;
}

.inputIcon {
  position: absolute;
  right: 0.7rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.1rem;
  height: 1.1rem;
  color: #94a3b8;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vscTableWrapper {
  overflow-x: auto;
  margin-bottom: 0;
}

.vscTable {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
  background: none;
}

.vscTableHeader {
  background: #f3f4f6;
  font-weight: 700;
  color: #64748b;
  font-size: 12px;
  border-bottom: 1px solid #e5e7eb;
  letter-spacing: 0.2px;
}

.vscTableCell {
  text-align: center;
  border-radius: 6px;
  position: relative;
  cursor: pointer;
  font-size: 14px;
  padding: 5px 8px;
  font-weight: 500;
  color: #64748b;
  background: none;
  border: none;
  outline: none;
  user-select: none;
  transition: background 0.15s, color 0.15s, border 0.15s;
}

.vscTableCell.selected {
  font-weight: 700;
  color: #2563eb;
  background: #e0e7ff;
  border: 1.2px solid #2563eb;
}

.pricingGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 12px 18px;
}

@media (max-width: 800px) {
  .pricingGrid {
    grid-template-columns: 1fr 1fr !important;
    gap: 8px 10px !important;
  }

  .pricingGrid label {
    font-size: 11px !important;
    margin-bottom: 1px !important;
    line-height: 1.2 !important;
  }

  .pricingGrid input {
    font-size: 14px !important;
    padding: 0.4rem 0.6rem !important;
  }

  .pricingGrid>div {
    gap: 1px !important;
  }
}

.optionRadioLabel {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
  color: #334155;
  font-weight: 400;
  padding: 2px 0;
  cursor: pointer;
  border-radius: 6px;
  transition: background 0.13s;
}

.optionRadioLabel.selected {
  background: #e0e7ff;
  color: #2563eb;
  font-weight: 600;
}

.optionRadioLabel:hover {
  background: #f1f5f9;
}

.optionRadio {
  accent-color: #2563eb;
  width: 18px;
  height: 18px;
  margin-right: 6px;
  cursor: pointer;
}

.optionSection {
  margin-bottom: 18px;
}

.optionContent {
  margin-top: 8px;
}

.optionTitle {
  font-weight: 600;
  font-size: 1.05rem;
  color: #334155;
  margin-bottom: 8px;
}

.paymentShortInput {
  max-width: 160px;
  width: 100%;
  display: inline-block;
  color: #334155;
}

/* Remove number input spinners/arrows for all browsers */
.paymentShortInput[type=number] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

.paymentShortInput[type=number]::-webkit-outer-spin-button,
.paymentShortInput[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.centeredVinGroup {
  width: 360px;
  margin: 0 auto 18px auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.vinHighlight {
  background: linear-gradient(90deg, #e0f0ff 0%, #b6d6ff 100%);
  border: 3px solid #2563eb;
  border-radius: 9px;
  box-shadow: 0 4px 18px 0 rgba(37, 99, 235, 0.18), 0 0 0 2px #93c5fd;
  transition: box-shadow 0.2s, border 0.2s, background 0.2s;
  padding: 0.6rem 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.orderTabs {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #e5e7eb;
  gap: 0;
  background: #f9fafb;
  border-radius: 8px 8px 0 0;
  position: relative;
}

.orderTab {
  padding: 0.7rem 1.6rem;
  font-size: 1.08rem;
  font-weight: 500;
  color: #64748b;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  transition: color 0.18s, border 0.18s, background 0.18s;
  border-radius: 8px 8px 0 0;
  outline: none;
  margin-right: 0;
}

.orderTab.active {
  color: #dc2626;
  border-bottom: 3px solid #dc2626;
  background: #fff;
  font-weight: 700;
}

.orderTab:hover,
.orderTab:focus {
  color: #dc2626;
  background: #f3f6fa;
  border-bottom: 3px solid #dc2626;
}

.orderTab.active:hover,
.orderTab.active:focus {
  color: #dc2626;
  background: #fff;
  border-bottom: 3px solid #dc2626;
}

.orderTab:focus {
  outline: none;
  color: #dc2626;
}

.orderTab:not(:last-child) {
  border-right: 1px solid #e5e7eb;
}

@media (max-width: 800px) {
  .orderTabs {
    overflow-x: auto;
    white-space: nowrap;
    border-radius: 0;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
  }

  .orderTabs::-webkit-scrollbar {
    height: 3px;
    background: #f1f1f1;
  }

  .orderTabs::-webkit-scrollbar-thumb {
    background: #e5e7eb;
    border-radius: 2px;
  }

  .orderTabs::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    width: 32px;
    height: 100%;
    pointer-events: none;
    background: linear-gradient(to left, #f9fafb 80%, rgba(249, 250, 251, 0));
    z-index: 2;
    display: block;
  }

  .orderTab {
    flex: 0 0 auto !important;
    min-width: 48px !important;
    max-width: 56px !important;
    height: 30px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .orderTab span {
    display: block;
    transform: rotate(-90deg);
    white-space: nowrap;
    font-size: 0.9rem;
    line-height: 1.1;
  }
}

@media (min-width: 800px) {
  .orderTab {
    flex: 1 1 0;
    min-width: 0;
    white-space: normal;
  }
}

.coverageEssential {
  background: #f4f4f6;
  color: #64748b;
  font-weight: 500;
}

.coverageBeyond {
  background: #e5e7eb;
  color: #334155;
  font-weight: 600;
}

.coverageBest {
  background: linear-gradient(90deg, #fecaca 0%, #fca5a5 40%, #f87171 80%, #f87171 100%);
  color: #b91c1c;
  font-weight: 800;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 16px rgba(185, 28, 28, 0.13);
}

.recommendedBadge {
  background: #fbbf24;
  color: #92400e;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 4px;
  margin-right: 4px;
}

.sectionCard {
  border-radius: 18px;
  box-shadow: 0 4px 24px 0 rgba(30, 41, 59, 0.13);
  padding: 2.2rem 2.2rem 1.7rem 2.2rem;
  margin-bottom: 2.5rem;
  position: relative;
  color: #f3f3f3;
}

.sectionCard.coverage {
  background: linear-gradient(90deg, #1e293b 0%, #2563eb 100%);
}

.sectionCard.surcharges {
  background: linear-gradient(90deg, #065f46 0%, #059669 100%);
}

.sectionCard.payment {
  background: linear-gradient(90deg, #78350f 0%, #b45309 100%);
}

.sectionHeader {
  font-size: 1.18rem;
  font-weight: 700;
  margin-bottom: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.7rem;
  color: #fff;
}

.sectionHeader.surcharges {
  color: #059669;
}

.sectionHeader.payment {
  color: #b45309;
}

.fixedTable {
  table-layout: fixed;
  width: 100%;
}

.coverageTableHeadCell {
  width: 28.33%;
  text-align: center;
  min-height: 48px;
  padding-top: 14px;
  padding-bottom: 14px;
}

.coverageTableHeadCellTerm {
  width: 15%;
  text-align: left;
}

.coverageTableHeadCellTermAuto {
  width: 100px !important;
  white-space: nowrap;
  text-align: left;
}

.coverageSelected {
  border: 2.5px solid #dc2626 !important;
  box-shadow: 0 0 0 4px #2563eb22;
  background: linear-gradient(90deg, #e0e7ff 0%, #f0f6ff 100%) !important;
  color: #1e293b !important;
  font-weight: 700;
  z-index: 2;
  transition: background 0.18s, box-shadow 0.18s, border 0.18s;
}

.sparkleEffect {
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.sparkleEffect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -60%;
  width: 160%;
  height: 100%;
  pointer-events: none;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.0) 55%, rgba(255, 255, 255, 0.38) 75%, rgba(255, 255, 255, 0.18) 90%, rgba(255, 255, 255, 0.0) 100%);
  animation: fadeRightShimmer 3.2s infinite cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 0;
}

@keyframes fadeRightShimmer {
  0% {
    left: -60%;
    opacity: 0.35;
  }

  30% {
    opacity: 1;
  }

  70% {
    opacity: 1;
  }

  100% {
    left: 100%;
    opacity: 0.35;
  }
}

.vinInputHighlight {
  border: 2.5px solid #9f9fa1 !important;
  background: linear-gradient(90deg, #e0f2fe 0%, #dbeafe 100%);
  box-shadow: 0 0 0 2.5px #93c5fd44, 0 2px 12px #2563eb22;
  color: #9f9fa1;
  font-weight: 700;
  font-size: 1.13rem;
  letter-spacing: 0.04em;
  transition: box-shadow 0.18s, border 0.18s, background 0.18s;
}

.vinSuccessMsgInline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.01rem;
  font-weight: 600;
  color: #166534;
  background: linear-gradient(90deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1.5px solid #bbf7d0;
  border-radius: 7px;
  padding: 0.18rem 0.7rem;
  margin-bottom: 0.4rem;
  margin-left: 2px;
}

.coverageTableHeadCell,
.coverageEssential,
.coverageBeyond,
.coverageBest {
  border: 2.5px solid transparent;
}

.vehicle-info-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 14px;
  width: 100%;
}

@media (max-width: 600px) {
  .vehicle-info-grid {
    grid-template-columns: 1fr !important;
    gap: 8px !important;
  }
}

@media (max-width: 600px) {
  .centeredVinGroup {
    width: 100% !important;
    min-width: 0 !important;
    padding: 0 0.5rem !important;
    margin: 0 0 18px 0 !important;
    align-items: stretch !important;
  }

  .inputWithIcon {
    width: 100% !important;
    flex-direction: row !important;
    align-items: center !important;
  }

  .inputField,
  .input-group input,
  .input-group select {
    width: 100% !important;
    min-width: 0 !important;
    font-size: 1rem !important;
    padding: 0.7rem 0.7rem !important;
    box-sizing: border-box !important;
  }

  .input-group {
    width: 100% !important;
    min-width: 0 !important;
    margin-bottom: 10px !important;
  }

  .orderTabs {
    overflow-x: auto !important;
    flex-wrap: nowrap !important;
    gap: 0.2rem !important;
    margin-bottom: 1rem !important;
    width: 100vw !important;
    box-sizing: border-box !important;
  }

  .orderTab {
    padding: 0.5rem 0.7rem !important;
    font-size: 0.97rem !important;
    min-width: 100px !important;
    white-space: nowrap !important;
  }

  .inputGrid4,
  .inputGrid3 {
    grid-template-columns: 1fr !important;
    gap: 8px !important;
  }

  .optionSection,
  .optionContent {
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
  }

  .vscTableWrapper {
    overflow-x: auto;
    min-width: 320px;
  }

  .vscTableCell {
    padding: 4px 4px;
    font-size: 12px;
  }

  .coverageTableHeadCell,
  .coverageTableHeadCellTerm,
  .coverageTableHeadCellTermAuto {
    width: auto !important;
    font-size: 12px;
    padding-top: 6px;
    padding-bottom: 6px;
  }

  .vehicle-info-grid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 8px !important;
    width: 100% !important;
  }

  .input-group {
    width: 100% !important;
    min-width: 0 !important;
    margin-bottom: 10px !important;
  }
}

.sectionCardCustom {
  border: 1px solid #e5e7eb;
  border-radius: 14px;
  padding: 1.5rem 1.5rem 1.7rem 1.5rem;
  background: #f9fafb;
  font-family: 'Inter', 'Segoe UI', 'system-ui', 'Arial', sans-serif;
}

.sectionTitleCustom {
  font-weight: 800;
  font-size: 1.1rem;
  color: #1e293b;
  margin-bottom: 8px;
  letter-spacing: -0.01em;
}

.dividerLineCustom {
  height: 1px;
  background: #e5e7eb;
  margin-bottom: 22px;
}

.flexColGap16 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.vehicleInfoGrid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 14px;
  width: 100%;
}

@media (max-width: 600px) {
  .vehicleInfoGrid {
    grid-template-columns: 1fr !important;
    gap: 8px !important;
  }
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.inputWithIcon {
  display: flex;
  align-items: center;
}

.inputSmall {
  width: 100%;
}

.inputFullWidth {
  width: 100%;
}

.inputIcon,
.inlineIcon18 {
  position: absolute;
  right: 0.7rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.1rem;
  height: 1.1rem;
  color: #94a3b8;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 800px) {
  .toggleGroup.toggleGroup {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
    padding: 0.5rem !important;
    background: #f8fafc !important;
    border-radius: 8px !important;
    border: 1px solid #e2e8f0 !important;
  }

  .toggleButton.toggleButton {
    width: 100% !important;
    min-width: 0 !important;
    font-size: 0.875rem !important;
    padding: 0.75rem 1rem !important;
    border-radius: 6px !important;
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    color: #475569 !important;
    font-weight: 500 !important;
    text-align: center !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.2s ease !important;
    outline: none;
  }

  .toggleButtonActive.toggleButtonActive {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    border: 1px solid #dc2626 !important;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15) !important;
    transform: translateY(-1px) !important;
    outline: none;
  }

  .toggleButton.toggleButton:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }
}

@media (max-width: 800px) {
  .optionRadioLabel.optionRadioLabel {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    font-size: 0.875rem !important;
    color: #475569 !important;
    font-weight: 500 !important;
    padding: 0.75rem 1rem !important;
    margin-bottom: 0.5rem !important;
    cursor: pointer !important;
    border-radius: 6px !important;
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.2s ease !important;
  }

  .optionRadioLabel.selected {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    border: 1px solid #dc2626 !important;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15) !important;
    transform: translateY(-1px) !important;
  }

  .optionRadioLabel:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  .optionRadio {
    display: none !important;
  }
}

@media (max-width: 800px) {
  .optionRadioLabel.optionRadioLabel {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    font-size: 0.875rem !important;
    color: #475569 !important;
    font-weight: 500 !important;
    padding: 0.75rem 1rem !important;
    margin-bottom: 0.5rem !important;
    cursor: pointer !important;
    border-radius: 6px !important;
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.2s ease !important;
  }

  .optionRadioLabel.selected {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    border: 1px solid #dc2626 !important;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15) !important;
    transform: translateY(-1px) !important;
  }

  .optionRadioLabel:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  .optionRadio {
    display: none !important;
  }
}

@media (max-width: 800px) {
  .optionRadioLabel.optionRadioLabel {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    font-size: 0.875rem !important;
    color: #475569 !important;
    font-weight: 500 !important;
    padding: 0.75rem 1rem !important;
    margin-bottom: 0.5rem !important;
    cursor: pointer !important;
    border-radius: 6px !important;
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.2s ease !important;
  }

  .optionRadioLabel.selected {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    border: 1px solid #dc2626 !important;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15) !important;
    transform: translateY(-1px) !important;
  }

  .optionRadioLabel:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  .optionRadio {
    display: none !important;
  }
}

@media (max-width: 800px) {
  .tableScrollWrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    margin: 0 -0.3rem;
  }

  .tableScrollWrapper::-webkit-scrollbar {
    height: 4px;
    background: #f1f5f9;
  }

  .tableScrollWrapper::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
  }

  .fixedTable {
    min-width: 480px;
    table-layout: auto;
    pointer-events: none;
  }

  .fixedTable td {
    pointer-events: auto;
  }

  .coverageTableHeadCell,
  .coverageTableHeadCellTermAuto {
    white-space: nowrap;
    min-width: 100px;
  }

  .coverageTableHeadCellTermAuto {
    min-width: 60px;
  }
}

/* Desktop/Mobile Toggle Classes */
.desktopOnly {
  display: block;
}

.mobileOnly {
  display: none;
}

@media (max-width: 800px) {
  .desktopOnly {
    display: none !important;
  }

  .mobileOnly {
    display: block !important;
  }
}

/* Mobile VSC Interface */
.mobileCoverageSelector {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: visible;
}

.mobileCoverageTab {
  flex: 1;
  min-width: 90px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  text-align: center;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: visible;
}

.mobileCoverageTab:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #cbd5e1;
}

.mobileCoverageTab.coverageSelected {
  border-color: currentColor;
  background: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.mobileCoverageTabContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.mobileCoverageTabTitle {
  font-size: 13px;
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

/* Font weights match desktop coverage types */
.mobileCoverageTab.coverageEssential .mobileCoverageTabTitle {
  font-weight: 500;
}

.mobileCoverageTab.coverageBeyond .mobileCoverageTabTitle {
  font-weight: 600;
}

.mobileCoverageTab.coverageBest .mobileCoverageTabTitle {
  font-weight: 800;
}


.mobileCoverageTabSubtitle {
  font-size: 11px;
  font-weight: 400;
  color: #64748b;
  line-height: 1.2;
}

.mobileCoverageTabBadge {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-radius: 12px;
  padding: 0.25rem 0.5rem;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  margin-top: 0.25rem;
  display: inline-block;
}

.recommendedBadgeText {
  font-size: 9px;
  font-weight: 600;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.recommendedTab {
  position: relative;
}

.recommendedTab::after {
  position: relative;
}

.recommendedTab::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 11px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.recommendedTab.coverageSelected::before {
  opacity: 1;
}

.mobileSelectedCoverage {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.mobileSelectedTitle {
  font-size: 16px;
  font-weight: 600;
  color: #334155;
  margin-bottom: 0.5rem;
}

.mobileSelectedDescription {
  font-size: 13px;
  color: #64748b;
  line-height: 1.4;
}

.mobileTermGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.mobileTermGridItem {
  padding: 0.75rem 0.5rem;
  border: 2px solid transparent;
  border-radius: 8px;
  background: #f8fafc;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.mobileTermGridItem:hover {
  background: #f1f5f9;
  transform: translateY(-1px);
}

.mobileTermGridItem.coverageSelected {
  border-color: currentColor;
  background: rgba(59, 130, 246, 0.05);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.mobileTermGridTerm {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
}

.mobileTermGridPrice {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.mobileQuickSelect {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.mobileQuickSelectTitle {
  font-size: 14px;
  font-weight: 600;
  color: #334155;
  margin-bottom: 0.75rem;
}

.mobileQuickSelectButtons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobileQuickSelectBtn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border: 2px solid transparent;
  border-radius: 8px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
  font-size: 13px;
  font-weight: 500;
  color: #334155;
}

.mobileQuickSelectBtn:hover {
  background: #f1f5f9;
  transform: translateY(-1px);
}

.mobileQuickSelectPrice {
  font-weight: 600;
  color: #1e293b;
}

/* Coverage type specific colors for mobile - Match Desktop */
.mobileCoverageTab.coverageEssential {
  background: #f4f4f6;
  color: #64748b;
  border: 2.5px solid transparent;
  outline: none
}

.mobileCoverageTab.coverageEssential:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(100, 116, 139, 0.2);
}

.mobileCoverageTab.coverageEssential.coverageSelected {
  background: #f4f4f6;
  color: #64748b;
  border: 2.5px solid #64748b !important;
  box-shadow: 0 4px 20px rgba(100, 116, 139, 0.25);
  outline: none
}

.mobileCoverageTab.coverageBeyond {
  background: #e5e7eb;
  color: #334155;
  border: 2.5px solid transparent;
  outline: none
}

.mobileCoverageTab.coverageBeyond:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(51, 65, 85, 0.2);
}

.mobileCoverageTab.coverageBeyond.coverageSelected {
  background: #e5e7eb;
  color: #334155;
  border: 2.5px solid #334155 !important;
  box-shadow: 0 4px 20px rgba(51, 65, 85, 0.25);
  outline: none
}

.mobileCoverageTab.coverageBest {
  background: linear-gradient(90deg, #fecaca 0%, #fca5a5 40%, #f87171 80%, #f87171 100%);
  color: #b91c1c;
  border: 2.5px solid transparent;
  position: relative;
  outline: none;
}

.mobileCoverageTab.coverageBest:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(185, 28, 28, 0.2);
}

.mobileCoverageTab.coverageBest.coverageSelected {
  background: linear-gradient(90deg, #fecaca 0%, #fca5a5 40%, #f87171 80%, #f87171 100%) !important;
  color: #b91c1c !important;
  border: 2.5px solid #b91c1c !important;
  box-shadow: 0 4px 20px rgba(185, 28, 28, 0.25);
  outline: none
}

/* Term Grid Colors */
.mobileTermGridItem.coverageEssential {
  border-color: #64748b;
}

.mobileTermGridItem.coverageEssential.coverageSelected {
  background: rgba(100, 116, 139, 0.05);
  box-shadow: 0 2px 8px rgba(100, 116, 139, 0.15);
}

.mobileTermGridItem.coverageBeyond {
  border-color: #334155;
}

.mobileTermGridItem.coverageBeyond.coverageSelected {
  background: rgba(51, 65, 85, 0.05);
  box-shadow: 0 2px 8px rgba(51, 65, 85, 0.15);
}

.mobileTermGridItem.coverageBest {
  border-color: #b91c1c;
}

.mobileTermGridItem.coverageBest.coverageSelected {
  background: rgba(185, 28, 28, 0.05);
  box-shadow: 0 2px 8px rgba(185, 28, 28, 0.15);
}

/* Quick Select Colors */
.mobileQuickSelectBtn.coverageEssential {
  border-color: #64748b;
}

.mobileQuickSelectBtn.coverageBeyond {
  border-color: #334155;
}

.mobileQuickSelectBtn.coverageBest {
  border-color: #b91c1c;
}