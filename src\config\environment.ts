// Environment Configuration
// This file documents and validates environment variables

export const ENVIRONMENT = {
  // API Configuration
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000',
  
  // App Configuration
  APP_TITLE: import.meta.env.VITE_APP_TITLE || 'Vehicle Service Contract',
  DEBUG_MODE: import.meta.env.VITE_DEBUG_MODE === 'true',
  
  // Environment detection
  NODE_ENV: import.meta.env.MODE,
  IS_DEV: import.meta.env.DEV,
  IS_PROD: import.meta.env.PROD,
} as const;

// Validation function to ensure required environment variables are set
export function validateEnvironment(): void {
  const requiredVars: string[] = [
    // Add any required environment variables here
    // 'VITE_API_BASE_URL',
  ];
  
  const missingVars = requiredVars.filter((varName: string) => !import.meta.env[varName]);
  
  if (missingVars.length > 0) {
    console.warn('Missing environment variables:', missingVars);
  }
}

// Helper to get environment-specific configuration
export function getEnvironmentConfig() {
  return {
    api: {
      baseUrl: ENVIRONMENT.API_BASE_URL,
      timeout: ENVIRONMENT.IS_DEV ? 30000 : 10000, // Longer timeout in dev
    },
    app: {
      title: ENVIRONMENT.APP_TITLE,
      debug: ENVIRONMENT.DEBUG_MODE,
    },
  };
} 