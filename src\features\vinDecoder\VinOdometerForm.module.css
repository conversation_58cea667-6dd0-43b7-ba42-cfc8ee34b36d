.formContainer {
  width: 900px;
  max-width: 900px;
  margin: 0.75rem auto;
  padding: 1.25rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.9);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  z-index: 10;
}

.formContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #dc2626 0%, #b91c1c 50%, #dc2626 100%);
  z-index: 2;
}

.formContainer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(220, 38, 38, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

.title {
  color: #1e293b;
  text-align: center;
  font-size: 1.875rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.025em;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 3;
}

.subtitle {
  text-align: center;
  color: #64748b;
  margin: 0 0 1.5rem 0;
  font-size: 0.95rem;
  font-weight: 400;
  line-height: 1.5;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.25rem;
  margin-bottom: 1.5rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
}

.label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #334155;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.label::before {
  content: '';
  width: 3px;
  height: 12px;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  border-radius: 2px;
}

.input {
  padding: 0.875rem 1rem;
  font-size: 0.95rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  transition: all 0.2s ease-in-out;
  background-color: #ffffff;
  color: #1e293b;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  position: relative;
}

.input:focus {
  border-color: #dc2626;
  outline: none;
  background-color: #ffffff;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.input:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Special styling for VIN input */
.input[pattern] {
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  letter-spacing: 0.1em;
  font-weight: 600;
}

.buttonRow {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.button {
  min-width: 200px;
  padding: 0.875rem 2.25rem;
  font-size: 0.95rem;
  color: #ffffff;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  font-weight: 600;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.button:hover::before {
  left: 100%;
}

.button:hover:not(:disabled), .button:focus:not(:disabled) {
  background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.error {
  color: #dc2626;
  font-size: 0.8rem;
  margin-top: 0.375rem;
  font-weight: 500;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  background: rgba(220, 38, 38, 0.05);
  border-radius: 6px;
  border-left: 3px solid #dc2626;
}

.error::before {
  content: "⚠";
  font-size: 0.875rem;
  flex-shrink: 0;
}

/* Special styling for VIN validation errors */
.error.vinValidationError {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 0.75rem;
  margin-top: 0.75rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #991b1b;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.15);
  animation: shake 0.5s ease-in-out;
}

.error.vinValidationError::before {
  content: "🚫";
  font-size: 1rem;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

.loading {
  text-align: center;
  color: #dc2626;
  font-weight: 600;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(220, 38, 38, 0.05);
  border-radius: 8px;
}

.loading::before {
  content: "";
  width: 1rem;
  height: 1rem;
  border: 2px solid #dc2626;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.result {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  padding: 1.25rem;
  border-radius: 12px;
  margin-top: 1rem;
  border: 1px solid #bbf7d0;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.1);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.result::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #059669 0%, #10b981 50%, #059669 100%);
}

.result h3 {
  margin: 0 0 0.75rem 0;
  color: #166534;
  font-size: 1.1rem;
  font-weight: 700;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.result h3::before {
  content: '✓';
  color: #059669;
  font-weight: bold;
}

.result ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.25rem;
}

.result li {
  padding: 0.375rem 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 0.9rem;
  color: #1f2937;
  background: rgba(255, 255, 255, 0.7);
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  border: 1px solid rgba(187, 247, 208, 0.5);
}

.result strong {
  color: #166534;
  font-weight: 600;
  margin-right: 0.375rem;
}

.nextStepContainer {
  margin-top: 1rem;
  padding: 1.25rem;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 12px;
  border: 1px solid #f59e0b;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
  animation: slideInUp 0.4s ease-out;
  position: relative;
  overflow: hidden;
}

.nextStepContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 50%, #f59e0b 100%);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nextStepContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.successMessage {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #92400e;
  font-weight: 600;
  font-size: 0.95rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.successIcon {
  width: 1.125rem;
  height: 1.125rem;
  color: #059669;
  flex-shrink: 0;
}

.nextButton {
  min-width: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem 1.75rem;
  font-size: 0.95rem;
  color: #ffffff;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  font-weight: 600;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.nextButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nextButton:hover::before {
  left: 100%;
}

.nextButton:hover, .nextButton:focus {
  background: linear-gradient(135deg, #047857 0%, #065f46 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(5, 150, 105, 0.4);
}

.arrowIcon {
  width: 1rem;
  height: 1rem;
  transition: transform 0.2s ease-in-out;
}

.nextButton:hover .arrowIcon {
  transform: translateX(3px);
}

.demoCheckbox {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 8px;
  display: flex;
  align-items: left;
  justify-content: left;
}

.demoLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  color: #92400e;
}

.demoInput {
  width: 16px;
  height: 16px;
  accent-color: #dc2626;
  cursor: pointer;
}

.demoText {
  font-size: 0.875rem;
  user-select: none;
}

@media (max-width: 1024px) {
  .formContainer {
    width: 100%;
    max-width: 100%;
    margin: 0.5rem;
    padding: 1rem;
  }
  
  .title {
    font-size: 1.75rem;
  }
  
  .subtitle {
    font-size: 0.9rem;
    margin-bottom: 1.25rem;
  }
  
  .formRow {
    gap: 1rem;
    margin-bottom: 1.25rem;
  }
  
  .input {
    padding: 0.75rem 0.875rem;
    font-size: 0.9rem;
  }
  
  .button {
    min-width: 180px;
    padding: 0.75rem 2rem;
    font-size: 0.9rem;
  }
  
  .result {
    padding: 1rem;
  }
  
  .result h3 {
    font-size: 1rem;
  }
  
  .result li {
    font-size: 0.85rem;
  }
  
  .nextStepContainer {
    padding: 1rem;
  }
  
  .nextStepContent {
    gap: 0.75rem;
  }
  
  .successMessage {
    font-size: 0.9rem;
  }
  
  .nextButton {
    min-width: 180px;
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .formContainer {
    width: 100%;
    max-width: 100%;
    margin: 0.25rem;
    padding: 0.875rem;
  }
  
  .title {
    font-size: 1.5rem;
  }
  
  .subtitle {
    font-size: 0.85rem;
  }
  
  .formRow {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .input {
    padding: 0.625rem 0.75rem;
    font-size: 0.85rem;
  }
  
  .button {
    min-width: 160px;
    padding: 0.625rem 1.5rem;
    font-size: 0.85rem;
  }
  
  .result {
    padding: 0.875rem;
  }
  
  .result h3 {
    font-size: 0.95rem;
  }
  
  .result li {
    font-size: 0.8rem;
  }
  
  .nextStepContainer {
    padding: 0.875rem;
  }
  
  .successMessage {
    font-size: 0.85rem;
    text-align: center;
  }
  
  .nextButton {
    min-width: 160px;
    padding: 0.625rem 1.25rem;
    font-size: 0.85rem;
  }
  
  .error.vinValidationError {
    padding: 0.625rem;
    font-size: 0.8rem;
  }
}

@media (max-height: 600px) {
  .formContainer {
    width: 100%;
    max-width: 100%;
    margin: 0.25rem;
    padding: 0.75rem;
  }
  
  .title {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
  }
  
  .subtitle {
    font-size: 0.8rem;
    margin-bottom: 1rem;
  }
  
  .formRow {
    margin-bottom: 1rem;
  }
  
  .buttonRow {
    margin-bottom: 0.75rem;
  }
  
  .result {
    padding: 0.75rem;
    margin-top: 0.75rem;
  }
  
  .result h3 {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }
  
  .result li {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }
  
  .nextStepContainer {
    padding: 0.75rem;
    margin-top: 0.75rem;
  }
  
  .nextStepContent {
    gap: 0.5rem;
  }
  
  .successMessage {
    font-size: 0.8rem;
  }
  
  .nextButton {
    min-width: 140px;
    padding: 0.5rem 1.25rem;
    font-size: 0.8rem;
  }
}

.animatedForm {
  opacity: 0;
  max-height: 0;
  transform: translateY(-12px);
  transition: opacity 0.35s cubic-bezier(0.4,0,0.2,1), max-height 0.35s cubic-bezier(0.4,0,0.2,1), transform 0.35s cubic-bezier(0.4,0,0.2,1);
  overflow: hidden;
  pointer-events: none;
}
.animatedFormVisible {
  opacity: 1;
  max-height: 2000px;
  transform: translateY(0);
  pointer-events: auto;
} 
