import React, { createContext, useContext, useState, useCallback } from 'react';
import type { ReactNode } from 'react';
import { generateStepsForFlow, getFlowType, type FlowType } from '../constants/steps';

export interface StepStatus {
  [stepId: string]: {
    completed: boolean;
    current: boolean;
    accessible: boolean;
  };
}

export interface Step {
  id: string;
  label: string;
  description: string;
  completed: boolean;
  current: boolean;
  onClick?: () => void;
}

interface StepContextType {
  currentStepId: string;
  stepStatus: StepStatus;
  currentFlow: FlowType;
  stepHistory: string[];
  setCurrentStep: (stepId: string) => void;
  markStepCompleted: (stepId: string, completed?: boolean) => void;
  setStepAccessible: (stepId: string, accessible: boolean) => void;
  goToStep: (stepId: string) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  getCurrentStep: () => Step | undefined;
  isLastStep: () => boolean;
  isFirstStep: () => boolean;
  getProgress: () => number;
  initializeSteps: (steps: Step[], initialStep?: string) => void;
  initializeFlow: (selectedOption: string, hasLowLimit: boolean, initialStep?: string) => void;
  updateFlow: (selectedOption: string, hasLowLimit: boolean) => void;
  updateStepsOnly: (selectedOption: string, hasLowLimit: boolean) => void;
  getPreviousStep: () => string | undefined;
  getNextStepInFlow: (currentStep: string) => string | undefined;
  getCurrentSteps: () => Step[];
}

const StepContext = createContext<StepContextType | undefined>(undefined);

interface StepProviderProps {
  children: ReactNode;
}

export const StepProvider: React.FC<StepProviderProps> = ({ children }) => {
  const [currentStepId, setCurrentStepId] = useState<string>('');
  const [stepStatus, setStepStatus] = useState<StepStatus>({});
  const [steps, setSteps] = useState<Step[]>([]);
  const [currentFlow, setCurrentFlow] = useState<FlowType>('direct');
  const [stepHistory, setStepHistory] = useState<string[]>([]);

  const initializeSteps = useCallback((newSteps: Step[], initialStep?: string) => {
    setSteps(newSteps);
    const initialStepId = initialStep || newSteps[0]?.id || '';
    setCurrentStepId(initialStepId);
    
    const initialStatus: StepStatus = {};
    newSteps.forEach((step) => {
      initialStatus[step.id] = {
        completed: step.completed || false,
        current: step.id === initialStepId,
        accessible: true // Allow backward navigation by default
      };
    });
    setStepStatus(initialStatus);
  }, []);

  const setCurrentStep = useCallback((stepId: string) => {
    // Add current step to history before changing
    if (currentStepId && currentStepId !== stepId) {
      setStepHistory(prev => [...prev, currentStepId]);
    }

    setCurrentStepId(stepId);
    setStepStatus(prev => {
      const newStatus = { ...prev };
      Object.keys(newStatus).forEach(id => {
        newStatus[id] = {
          ...newStatus[id],
          current: id === stepId
        };
      });
      return newStatus;
    });
  }, [currentStepId]);

  const markStepCompleted = useCallback((stepId: string, completed: boolean = true) => {
    setStepStatus(prev => ({
      ...prev,
      [stepId]: {
        ...prev[stepId],
        completed
      }
    }));
  }, []);

  const setStepAccessible = useCallback((stepId: string, accessible: boolean) => {
    setStepStatus(prev => ({
      ...prev,
      [stepId]: {
        ...prev[stepId],
        accessible
      }
    }));
  }, []);

  const goToStep = useCallback((stepId: string) => {
    const step = steps.find(s => s.id === stepId);
    if (step && stepStatus[stepId]?.accessible) {
      setCurrentStep(stepId);
    }
  }, [steps, stepStatus, setCurrentStep]);

  const goToNextStep = useCallback(() => {
    const currentIndex = steps.findIndex(step => step.id === currentStepId);
    if (currentIndex < steps.length - 1) {
      const nextStep = steps[currentIndex + 1];
      setCurrentStep(nextStep.id);
      markStepCompleted(currentStepId, true);
    }
  }, [currentStepId, steps, setCurrentStep, markStepCompleted]);

  const goToPreviousStep = useCallback(() => {
    const currentIndex = steps.findIndex(step => step.id === currentStepId);
    if (currentIndex > 0) {
      const prevStep = steps[currentIndex - 1];
      setCurrentStep(prevStep.id);
    }
  }, [currentStepId, steps, setCurrentStep]);

  const getCurrentStep = useCallback(() => {
    return steps.find(step => step.id === currentStepId);
  }, [steps, currentStepId]);

  const isLastStep = useCallback(() => {
    const currentIndex = steps.findIndex(step => step.id === currentStepId);
    return currentIndex === steps.length - 1;
  }, [steps, currentStepId]);

  const isFirstStep = useCallback(() => {
    const currentIndex = steps.findIndex(step => step.id === currentStepId);
    return currentIndex === 0;
  }, [steps, currentStepId]);

  const getProgress = useCallback(() => {
    const completedSteps = Object.values(stepStatus).filter(status => status.completed).length;
    return steps.length > 0 ? (completedSteps / steps.length) * 100 : 0;
  }, [stepStatus, steps.length]);

  // New flow-based methods
  const initializeFlow = useCallback((selectedOption: string, hasLowLimit: boolean, initialStep?: string) => {
    const flowType = getFlowType(selectedOption, hasLowLimit);
    const flowSteps = generateStepsForFlow(flowType);

    setCurrentFlow(flowType);
    setSteps(flowSteps);
    setStepHistory([]);

    const initialStepId = initialStep || flowSteps[0]?.id || '';
    setCurrentStepId(initialStepId);

    const initialStatus: StepStatus = {};
    flowSteps.forEach((step) => {
      initialStatus[step.id] = {
        completed: step.completed || false,
        current: step.id === initialStepId,
        accessible: true
      };
    });
    setStepStatus(initialStatus);
  }, []);

  const updateFlow = useCallback((selectedOption: string, hasLowLimit: boolean) => {
    const newFlowType = getFlowType(selectedOption, hasLowLimit);
    if (newFlowType !== currentFlow) {
      const newSteps = generateStepsForFlow(newFlowType);
      setCurrentFlow(newFlowType);
      setSteps(newSteps);

      // Update step status for new steps, but preserve current step if it exists in new flow
      const newStatus: StepStatus = {};
      newSteps.forEach((step) => {
        newStatus[step.id] = {
          completed: stepStatus[step.id]?.completed || false,
          current: step.id === currentStepId,
          accessible: true
        };
      });
      setStepStatus(newStatus);
    }
  }, [currentFlow, currentStepId, stepStatus]);

  const updateStepsOnly = useCallback((selectedOption: string, hasLowLimit: boolean) => {
    const newFlowType = getFlowType(selectedOption, hasLowLimit);
    if (newFlowType !== currentFlow) {
      const newSteps = generateStepsForFlow(newFlowType);
      setCurrentFlow(newFlowType);
      setSteps(newSteps);

      // Update step status for new steps, but preserve current step and completion status
      const newStatus: StepStatus = {};
      newSteps.forEach((step) => {
        newStatus[step.id] = {
          completed: stepStatus[step.id]?.completed || false,
          current: step.id === currentStepId,
          accessible: true
        };
      });
      setStepStatus(newStatus);
    }
  }, [currentFlow, currentStepId, stepStatus]);

  const getPreviousStep = useCallback(() => {
    return stepHistory[stepHistory.length - 1];
  }, [stepHistory]);

  const getNextStepInFlow = useCallback((currentStep: string) => {
    const currentIndex = steps.findIndex(step => step.id === currentStep);
    if (currentIndex >= 0 && currentIndex < steps.length - 1) {
      return steps[currentIndex + 1].id;
    }
    return undefined;
  }, [steps]);

  const getCurrentSteps = useCallback(() => {
    return steps;
  }, [steps]);

  const value: StepContextType = {
    currentStepId,
    stepStatus,
    currentFlow,
    stepHistory,
    setCurrentStep,
    markStepCompleted,
    setStepAccessible,
    goToStep,
    goToNextStep,
    goToPreviousStep,
    getCurrentStep,
    isLastStep,
    isFirstStep,
    getProgress,
    initializeSteps,
    initializeFlow,
    updateFlow,
    updateStepsOnly,
    getPreviousStep,
    getNextStepInFlow,
    getCurrentSteps
  };

  return (
    <StepContext.Provider value={value}>
      {children}
    </StepContext.Provider>
  );
};

export const useStepContext = (): StepContextType => {
  const context = useContext(StepContext);
  if (context === undefined) {
    throw new Error('useStepContext must be used within a StepProvider');
  }
  return context;
}; 