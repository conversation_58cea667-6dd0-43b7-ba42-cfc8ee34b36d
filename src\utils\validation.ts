// Validation utilities
export const VIN_REGEX = /^[A-HJ-NPR-Z0-9]{17}$/i;

export function validateVin(vin: string): string | null {
  if (!vin) return 'VIN is required.';
  if (vin.length !== 17) return 'VIN must be exactly 17 characters.';
  if (!VIN_REGEX.test(vin)) return 'VIN contains invalid characters (I, O, Q are not allowed).';
  if (vin === '0'.repeat(17)) return 'VIN cannot be all zeros.';
  return null;
}

export function validateOdometer(odometer: string): string | null {
  if (!odometer) return 'Odometer is required.';
  if (!/^\d+$/.test(odometer)) return 'Odometer must be a number.';
  if (parseInt(odometer, 10) < 0) return 'Odometer cannot be negative.';
  return null;
}

export function validateEmail(email: string): string | null {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email) return 'Email is required.';
  if (!emailRegex.test(email)) return 'Please enter a valid email address.';
  return null;
}

export function validatePhone(phone: string): string | null {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  if (!phone) return 'Phone number is required.';
  if (!phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
    return 'Please enter a valid phone number.';
  }
  return null;
}

export function validateRequired(value: string, fieldName: string): string | null {
  if (!value || value.trim() === '') return `${fieldName} is required.`;
  return null;
}

export function validateMinLength(value: string, minLength: number, fieldName: string): string | null {
  if (value.length < minLength) return `${fieldName} must be at least ${minLength} characters.`;
  return null;
}

export function validateMaxLength(value: string, maxLength: number, fieldName: string): string | null {
  if (value.length > maxLength) return `${fieldName} must be no more than ${maxLength} characters.`;
  return null;
}

// Currency/Price validation
export function validateCurrency(value: string): string | null {
  if (!value) return null; // Allow empty for optional fields
  const numericValue = value.replace(/[^\d.]/g, '');
  if (!numericValue) return 'Please enter a valid amount.';
  const number = parseFloat(numericValue);
  if (isNaN(number)) return 'Please enter a valid amount.';
  if (number < 0) return 'Amount cannot be negative.';
  if (number > 999999.99) return 'Amount is too large.';
  return null;
}

// ZIP code validation
export function validateZipCode(zip: string): string | null {
  if (!zip) return 'ZIP code is required.';
  const zipRegex = /^\d{5}(-\d{4})?$/;
  if (!zipRegex.test(zip)) return 'Please enter a valid ZIP code (e.g., 12345 or 12345-6789).';
  return null;
}

// State validation (2-letter state codes)
export function validateState(state: string): string | null {
  if (!state) return 'State is required.';
  if (state.length !== 2) return 'Please enter a valid 2-letter state code.';
  if (!/^[A-Z]{2}$/i.test(state)) return 'State code must contain only letters.';
  return null;
}

// Numeric input validation
export function validateNumericInput(value: string, allowDecimal: boolean = true): string {
  if (allowDecimal) {
    return value.replace(/[^\d.]/g, '').replace(/(\..*)\./g, '$1'); // Allow only digits and one decimal point
  } else {
    return value.replace(/[^\d]/g, ''); // Allow only digits
  }
}

// Phone number formatting and validation
export function formatPhoneNumber(value: string): string {
  const cleaned = value.replace(/\D/g, '');
  if (cleaned.length <= 3) return cleaned;
  if (cleaned.length <= 6) return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
  return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
}

// Currency formatting
export function formatCurrencyInput(value: string): string {
  const numericValue = value.replace(/[^\d.]/g, '');
  if (!numericValue) return '';
  const number = parseFloat(numericValue);
  if (isNaN(number)) return '';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(number);
}

// Remove currency formatting for editing
export function removeCurrencyFormatting(value: string): string {
  return value.replace(/[^\d.]/g, '');
}