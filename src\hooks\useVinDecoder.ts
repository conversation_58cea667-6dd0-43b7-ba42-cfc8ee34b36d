import { useState, useCallback } from 'react';
import { decodeVin, extractTrimFromRaw, validateVinFormat } from '../services/vinDecoderApi';
import type { VinData, VinDecodeResponse, VinFormErrors } from '../types/vin';

interface UseVinDecoderReturn {
  vinData: VinData;
  errors: VinFormErrors;
  loading: boolean;
  decoded: VinDecodeResponse | null;
  showNextStep: boolean;
  setVinData: (data: Partial<VinData>) => void;
  validateForm: () => boolean;
  handleDecode: () => Promise<void>;
  handleNextStep: () => void;
  resetForm: () => void;
}

const initialVinData: VinData = {
  input_36: '',   // VIN
  input_4: '',    // Year
  input_5: '',    // Make
  input_6: '',    // Model
  input_7: '',    // Trim
  input_138: '',  // Odometer
  // Hidden fields for Gravity Forms compatibility
  input_11: '',   // Body Style
  input_8: '',    // Engine
  input_9: '',    // Cylinders
  input_10: '',   // Drive Type
  input_37: ''    // Vehicle Class
};

const initialErrors: VinFormErrors = {};

export function useVinDecoder(onNext: (data: VinData) => void): UseVinDecoderReturn {
  const [vinData, setVinDataState] = useState<VinData>(initialVinData);
  const [errors, setErrors] = useState<VinFormErrors>(initialErrors);
  const [loading, setLoading] = useState(false);
  const [decoded, setDecoded] = useState<VinDecodeResponse | null>(null);
  const [showNextStep, setShowNextStep] = useState(false);

  const setVinData = useCallback(function(data: Partial<VinData>) {
    setVinDataState(function(prev) {
      return { ...prev, ...data };
    });
  }, []);

  const validateForm = useCallback(function(): boolean {
    const newErrors: VinFormErrors = {};

    // VIN validation (input_36)
    if (!vinData.input_36) {
      newErrors.input_36 = 'VIN is required.';
    } else if (vinData.input_36.length !== 17) {
      newErrors.input_36 = 'VIN must be exactly 17 characters.';
    } else if (!validateVinFormat(vinData.input_36)) {
      newErrors.input_36 = 'VIN contains invalid characters (I, O, Q are not allowed).';
    }

    // Odometer validation (input_138)
    if (!vinData.input_138) {
      newErrors.input_138 = 'Odometer is required.';
    } else if (!/^\d+$/.test(vinData.input_138)) {
      newErrors.input_138 = 'Odometer must be a number.';
    } else if (parseInt(vinData.input_138, 10) < 0) {
      newErrors.input_138 = 'Odometer cannot be negative.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [vinData]);

  const validateDecodedResult = useCallback(function(data: VinDecodeResponse): string | null {
    const hasValidMake = data.make && data.make.trim() !== '' && data.make !== 'N/A' && data.make !== 'null';
    const hasValidModel = data.model && data.model.trim() !== '' && data.model !== 'N/A' && data.model !== 'null';
    
    if (!hasValidMake || !hasValidModel) {
      return 'Invalid or unrecognized VIN. Please check the VIN number and try again.';
    }
    
    return null;
  }, []);

  const handleDecode = useCallback(async function() {
    if (!validateForm()) return;

    setLoading(true);
    setDecoded(null);
    setErrors(initialErrors);
    setShowNextStep(false);

    try {
      const data = await decodeVin(vinData.input_36);
      
      const validationError = validateDecodedResult(data);
      if (validationError) {
        setErrors({ general: validationError });
        setLoading(false);
        return;
      }

      setDecoded(data);
      setShowNextStep(true);
      setLoading(false);
    } catch (error: any) {
      setErrors({ general: error.message || 'Failed to decode VIN.' });
      setLoading(false);
    }
  }, [vinData, validateForm, validateDecodedResult]);

  const handleNextStep = useCallback(function() {
    if (decoded) {
      const trim = decoded.trim || extractTrimFromRaw(decoded.raw);
      const nextData: VinData = {
        input_36: decoded.vin,
        input_4: decoded.year || '',
        input_5: decoded.make || '',
        input_6: decoded.model || '',
        input_7: trim,
        input_138: vinData.input_138,
        // Populate hidden fields from VIN decode
        input_11: decoded.bodyStyle || '',
        input_8: decoded.engine || '',
        input_9: decoded.cylinders || '',
        input_10: decoded.driveType || '',
        input_37: decoded.vehicleClass || ''
      };
      onNext(nextData);
    }
  }, [decoded, vinData.input_138, onNext]);

  const resetForm = useCallback(function() {
    setVinDataState(initialVinData);
    setErrors(initialErrors);
    setDecoded(null);
    setShowNextStep(false);
    setLoading(false);
  }, []);

  return {
    vinData,
    errors,
    loading,
    decoded,
    showNextStep,
    setVinData,
    validateForm,
    handleDecode,
    handleNextStep,
    resetForm
  };
} 