// Performance utilities

// Debounce function to limit how often a function can be called
export function debounce(func: Function, wait: number): Function {
  let timeout: number;
  
  return function(...args: any[]) {
    clearTimeout(timeout);
    timeout = setTimeout(function() {
      func.apply(null, args);
    }, wait);
  };
}

// Throttle function to limit function execution rate
export function throttle(func: Function, limit: number): Function {
  let inThrottle: boolean;
  
  return function(...args: any[]) {
    if (!inThrottle) {
      func.apply(null, args);
      inThrottle = true;
      setTimeout(function() {
        inThrottle = false;
      }, limit);
    }
  };
}

// Simple memoization utility
export function memoize(func: Function): Function {
  const cache: Record<string, any> = {};
  
  return function(...args: any[]) {
    const key = JSON.stringify(args);
    
    if (cache[key] !== undefined) {
      return cache[key];
    }
    
    const result = func.apply(null, args);
    cache[key] = result;
    return result;
  };
}

// Lazy loading utility for components
export function lazyLoad<T>(
  importFunc: () => Promise<{ default: T }>
): () => Promise<T> {
  let promise: Promise<T> | null = null;
  
  return () => {
    if (!promise) {
      promise = importFunc().then(module => module.default);
    }
    return promise;
  };
}

// Intersection Observer utility for lazy loading
export function createIntersectionObserver(
  callback: IntersectionObserverCallback,
  options: IntersectionObserverInit = {}
): IntersectionObserver {
  return new IntersectionObserver(callback, {
    root: null,
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  });
}

// Performance monitoring utility
export function measurePerformance(name: string, fn: Function): any {
  const start = performance.now();
  const result = fn();
  const end = performance.now();
  
  console.log(name + ' took ' + (end - start).toFixed(2) + 'ms');
  return result;
}

// Async performance monitoring
export async function measureAsyncPerformance(name: string, fn: Function): Promise<any> {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  
  console.log(name + ' took ' + (end - start).toFixed(2) + 'ms');
  return result;
} 