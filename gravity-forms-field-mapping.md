# Gravity Forms Field Mapping - Form ID 98

## 📋 **Complete Input Field List**

### **🚗 Vehicle Information**
| Field ID | Name | Label | Type | Default Value |
|----------|------|-------|------|---------------|
| input_98_120 | input_120 | User Status | text | "demo" |
| input_98_425 | input_425 | User ID | text | "114" |
| input_98_426 | input_426 | Demo? | text | "Demo" |
| input_98_36 | input_36 | VIN | text | "JTMBFREV5JJ213506" |
| input_98_4 | input_4 | Year | text | "2018" |
| input_98_5 | input_5 | Make | text | "TOYOTA" |
| input_98_6 | input_6 | Model | text | "RAV4" |
| input_98_7 | input_7 | Trim | text | "LE" |
| input_98_11 | input_11 | Series | text | "ASA44L/ASA42L/AVA44L" |
| input_98_8 | input_8 | Engine | text | "2AR-FE" |
| input_98_9 | input_9 | Transmission | text | "6" |
| input_98_10 | input_10 | Drive Type | text | "4WD/4-Wheel Drive/4x4" |
| input_98_138 | input_138 | Vehicle Odometer | number | "85000" |
| input_98_53 | input_53 | Hidden Odometer | text | "" |
| input_98_37 | input_37 | Class | text | "Class 1" |

### **💰 Pricing Fields (Currency)**
| Field ID | Name | Label | Type | Default Value |
|----------|------|-------|------|---------------|
| input_98_71 | input_71 | Cash Price of Vehicle | currency | "$0.00" |
| input_98_123 | input_123 | Accessories / Add-Ons | currency | "$0.00" |
| input_98_124 | input_124 | Documentary Fee | currency | "$0.00" |
| input_98_173 | input_173 | Other Expense | currency | "$0.00" |
| input_98_117 | input_117 | Trade-In Value | currency | "$0.00" |
| input_98_126 | input_126 | Balance Owed on Trade In | currency | "$0.00" |
| input_98_127 | input_127 | Net Trade in Allowance | currency | "" (calculated) |
| input_98_128 | input_128 | Cash Deposit | currency | "$0.00" |
| input_98_72 | input_72 | Down Payment | currency | "$0.00" |
| input_98_129 | input_129 | Rebate | currency | "$0.00" |
| input_98_174 | input_174 | Other Credit | currency | "$0.00" |
| input_98_133 | input_133 | Total Loan Amount | currency | "" (calculated) |
| input_98_140 | input_140 | Total Loan Amount (Alt) | currency | "" |

### **🎯 Coverage Selection**
| Field ID | Name | Label | Type | Options |
|----------|------|-------|------|---------|
| input_98_83 | input_83 | Coverage Level | radio | Essential, Beyond Essential, Industry Best |
| input_98_13 | input_13 | Available Contract Terms | radio | 12-120 Mo/Unlimited |

### **📊 Contract Pricing**
| Field ID | Name | Label | Type | Default Value |
|----------|------|-------|------|---------------|
| input_98_89 | input_89 | Essential Price | currency | "$178.00" |
| input_98_107 | input_107 | Beyond Essential Price | currency | "$200.00" |
| input_98_108 | input_108 | Industry Best Price | currency | "$300.00" |
| input_98_176 | input_176 | Discount Tier 1 | currency | "$25.00" |
| input_98_201 | input_201 | Discount Tier 2 | currency | "$50.00" |
| input_98_202 | input_202 | Discount Tier 3 | currency | "$100.00" |

### **🔧 Order Method Selection**
| Field ID | Name | Label | Type | Options |
|----------|------|-------|------|---------|
| input_98_411 | input_411 | Favorite Ordering Method (hidden) | text | "Generating a Buyers Order, Entering Loan Amount Only, VSC Details Only, Recommend a Contract" |
| input_98_413 | input_413 | Order Method Radio | radio | Generating a Buyers Order, Entering Loan Amount Only, VSC Details Only, Recommend a Contract |
| input_98_414 | input_414 | Continue by: indicator (hidden) | text | "" |
| input_98_137 | input_137 | Continue By | radio | Same as 413 |

### **💳 Payment Calculator**
| Field ID | Name | Label | Type | Default Value |
|----------|------|-------|------|---------------|
| input_98_258 | input_258 | Loan Term (Months) | number | "72" |
| input_98_34 | input_34 | Interest Rate | number | "6.74" |
| input_98_30 | input_30 | Loan Amount | currency | "" (calculated) |
| input_98_82 | input_82 | VSC Final Price | currency | "" |
| input_98_276 | input_276 | Down Payment (calc) | currency | "" |
| input_98_332 | input_332 | Estimated Monthly Payment | currency | "" (calculated) |
| input_98_96 | input_96 | Est. Monthly Payment W/O VSC | currency | "" (calculated) |
| input_98_255 | input_255 | Payment Difference | currency | "" (calculated) |
| input_98_33 | input_33 | Monthly Payment | currency | "" (calculated) |
| input_98_330 | input_330 | VSC Monthly Payment | currency | "" (calculated) |

### **🏢 Dealer Information**
| Field ID | Name | Label | Type | Default Value |
|----------|------|-------|------|---------------|
| input_98_239 | input_239 | Dealer Name | text | "Demo Dealer" |
| input_98_240 | input_240 | Dealer Phone | text | "8888888888" |
| input_98_241 | input_241 | Dealer Fax | text | "5555555555" |
| input_98_304 | input_304 | Dealer Website | text | "www.demo.com" |
| input_98_324 | input_324 | Dealer Email | text | "<EMAIL>" |
| input_98_323 | input_323 | Demo Dealer | text | "Yes" |

### **👤 Customer Information**
| Field ID | Name | Label | Type | Default Value |
|----------|------|-------|------|---------------|
| input_98_243 | input_243 | Customer Name | name | Various subfields |
| input_98_333 | input_333 | Customer Email | email | "<EMAIL>" |
| input_98_261 | input_261 | Customer Address | text | "123 Demo Dr" |
| input_98_262 | input_262 | Customer City | text | "Demo Town" |
| input_98_263 | input_263 | Customer State | text | "UT" |
| input_98_264 | input_264 | Customer ZIP | text | "84321" |

### **🏦 Lienholder Information**
| Field ID | Name | Label | Type | Options |
|----------|------|-------|------|---------|
| input_98_291 | input_291 | Lienholder Selection | radio | Show all Lienholders, Show my existing Lienholders, Add a lienholder |
| input_98_290 | input_290 | All Lienholders Dropdown | select | Dynamic |
| input_98_301 | input_301 | Existing Lienholders Dropdown | select | Dynamic |
| input_98_292 | input_292 | New Lienholder Name | text | "" |
| input_98_293 | input_293 | New Lienholder Phone | text | "" |
| input_98_334 | input_334 | New Lienholder Email | text | "" |
| input_98_294 | input_294 | New Lienholder Address | address | Various subfields |

### **📝 Contract Details**
| Field ID | Name | Label | Type | Default Value |
|----------|------|-------|------|---------------|
| input_98_321 | input_321 | Contract Number | text | "REV0000000015" |
| input_98_250 | input_250 | Coverage Limit | text | "Unlimited" |
| input_98_245 | input_245 | Start Date | date | "07/22/2025" |
| input_98_246 | input_246 | End Date | date | "07/22/2025" |
| input_98_277 | input_277 | Contract Date | date | "07/22/2025" |

### **🎛️ Advanced Options**
| Field ID | Name | Label | Type | Default Value |
|----------|------|-------|------|---------------|
| input_98_289 | input_289 | Beyond Essential Upgrade | currency | "+ $2,500" |
| input_98_325 | input_325 | Industry Best Upgrade | currency | "+ $7,500" |
| input_98_415 | input_415 | Markup Options | radio | Various |
| input_98_416 | input_416 | Edit Markup Amount | currency | "" |

### **✍️ Signatures**
| Field ID | Name | Label | Type |
|----------|------|-------|------|
| input_98_302 | input_98_302_data | Buyer's Signature | signature |
| input_98_303 | input_98_303_data | Dealer Representative Signature | signature |

### **⚖️ Legal & Consent**
| Field ID | Name | Label | Type |
|----------|------|-------|------|
| input_98_230 | input_230.1 | Legal Consent | checkbox |
| input_98_367 | input_367 | Use Dealer Info | radio |

### **🔢 Calculation Fields (Hidden)**
| Field ID | Name | Label | Type | Purpose |
|----------|------|-------|------|---------|
| input_98_347 | input_347 | Method indication number | number | 1=Contract Builder, 2=Suggested, 3=Reduced Limit |
| input_98_227 | input_227 | Price Calculation | number | Internal calculations |
| input_98_267 | input_267 | Term Calculation | number | Internal calculations |
| input_98_392 | input_392 | Coverage Level Number | number | Internal calculations |

## 🎯 **Field Categories Summary**

- **Vehicle Info**: 15 fields
- **Pricing**: 13 currency fields  
- **Coverage**: 2 radio groups
- **Contract Pricing**: 6 currency fields
- **Order Method**: 3 selection fields
- **Payment Calculator**: 10 calculation fields
- **Dealer Info**: 6 text fields
- **Customer Info**: 7 fields
- **Lienholder**: 6 fields
- **Contract Details**: 5 fields
- **Advanced Options**: 3 fields
- **Signatures**: 2 signature fields
- **Legal**: 2 consent fields
- **Hidden Calculations**: 20+ calculation fields

**Total: 100+ input fields with complex conditional logic and real-time calculations**
