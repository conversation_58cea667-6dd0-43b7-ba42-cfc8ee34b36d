/* Import design system and shared styles */
@import './styles/design-system.css';
@import './styles/utilities.css';
@import './styles/inputs.css';
@import './styles/buttons.css';

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-x: hidden;
}

#root {
  min-height: 100vh;
  width: 100%;
  position: relative;
}

/* Main app background container */
.app-background {
  min-height: calc(100vh - 400px);
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* Background layers container */
.background-layers {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* SVG Car silhouette */
.car-silhouette {
  position: absolute;
  bottom: -5%;
  right: -10%;
  width: 70%;
  height: 50%;
  opacity: 0.5;
  transform: rotate(-8deg);
  animation: carFloat 8s ease-in-out infinite;
  z-index: 2;
}

.car-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
}

/* SVG Car element animations */
.car-headlight {
  animation: headlightGlow 3s ease-in-out infinite;
}

.car-taillight {
  animation: taillightGlow 4s ease-in-out infinite;
}

.car-wheel {
  animation: wheelSpin 10s linear infinite;
}

.car-body {
  filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.1));
}

.car-roof {
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.1));
}

.car-window {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.car-shadow {
  animation: shadowPulse 8s ease-in-out infinite;
}

/* Fog layers */
.fog-layer {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: fogDrift 12s ease-in-out infinite;
}

.fog-1 {
  animation-delay: 0s;
  opacity: 0.4;
  transform: scale(1.2);
}

.fog-2 {
  animation-delay: -4s;
  opacity: 0.3;
  transform: scale(1.5);
  animation-duration: 16s;
}

.fog-3 {
  animation-delay: -8s;
  opacity: 0.2;
  transform: scale(1.8);
  animation-duration: 20s;
}

/* Gradient overlay */
.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(220, 38, 38, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
    linear-gradient(135deg, rgba(15, 23, 42, 0.3) 0%, rgba(30, 41, 59, 0.2) 50%, rgba(51, 65, 85, 0.1) 100%);
  z-index: 2;
}

/* Floating particles */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 4;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: particleFloat var(--duration) ease-in-out infinite;
  animation-delay: var(--delay);
  left: var(--x);
  top: var(--y);
}

.particle:nth-child(even) {
  width: 3px;
  height: 3px;
  background: rgba(220, 38, 38, 0.4);
}

.particle:nth-child(3n) {
  width: 1px;
  height: 1px;
  background: rgba(59, 130, 246, 0.5);
}

/* Animations */
@keyframes carFloat {
  0%, 100% {
    transform: rotate(-8deg) translateY(0px) scale(1);
  }
  25% {
    transform: rotate(-7deg) translateY(-5px) scale(1.02);
  }
  50% {
    transform: rotate(-8deg) translateY(-10px) scale(1);
  }
  75% {
    transform: rotate(-9deg) translateY(-5px) scale(0.98);
  }
}

@keyframes headlightGlow {
  0%, 100% {
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.5)) drop-shadow(0 0 25px rgba(255, 255, 255, 0.2));
  }
}

@keyframes taillightGlow {
  0%, 100% {
    filter: drop-shadow(0 0 8px rgba(220, 38, 38, 0.2));
  }
  50% {
    filter: drop-shadow(0 0 12px rgba(220, 38, 38, 0.4)) drop-shadow(0 0 20px rgba(220, 38, 38, 0.1));
  }
}

@keyframes wheelSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes shadowPulse {
  0%, 100% {
    opacity: 0.1;
    transform: scaleX(1);
  }
  50% {
    opacity: 0.15;
    transform: scaleX(1.1);
  }
}

@keyframes fogDrift {
  0%, 100% {
    transform: translateX(-5%) translateY(-2%);
    opacity: 0.3;
  }
  25% {
    transform: translateX(2%) translateY(-5%);
    opacity: 0.4;
  }
  50% {
    transform: translateX(5%) translateY(-1%);
    opacity: 0.3;
  }
  75% {
    transform: translateX(-2%) translateY(-3%);
    opacity: 0.4;
  }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-40px) translateX(-5px);
    opacity: 0.6;
  }
  75% {
    transform: translateY(-20px) translateX(-15px);
    opacity: 0.8;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .car-silhouette {
    width: 80%;
    height: 40%;
    bottom: -3%;
    right: -15%;
  }

  .fog-layer {
    opacity: 0.2;
  }

  .fog-2 {
    opacity: 0.15;
  }

  .fog-3 {
    opacity: 0.1;
  }
}

@media (max-width: 480px) {
  .car-silhouette {
    width: 90%;
    height: 35%;
    bottom: -2%;
    right: -20%;
  }

  .particle {
    width: 1px;
    height: 1px;
  }
}

/* Ensure last child has proper spacing */
.app-background > *:last-child {
  margin-bottom: var(--spacing-8);
}