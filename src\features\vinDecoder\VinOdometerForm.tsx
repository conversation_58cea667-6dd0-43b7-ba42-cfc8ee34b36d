import React, { useState, useEffect } from 'react';
import styles from './VinOdometerForm.module.css';
import { useVinDecoder } from '../../hooks/useVinDecoder';

interface VinOdometerFormProps {
  onNext: (data: any) => void;
}

const VinOdometerForm: React.FC<VinOdometerFormProps> = ({ onNext }) => {
  const {
    vinData,
    errors,
    loading,
    showNextStep,
    setVinData,
    handleDecode,
    handleNextStep
  } = useVinDecoder(onNext);

  const [isDemoMode, setIsDemoMode] = useState(false);

  const handleDemoToggle = (checked: boolean) => {
    setIsDemoMode(checked);
    if (checked) {
      setVinData({
        input_36: '1HGCM82633A004352',
        input_138: '85000'
      });
    } else {
      setVinData({
        input_36: '',
        input_138: ''
      });
    }
  };

  useEffect(() => {
    if (showNextStep) {
      const timer = setTimeout(() => {
        handleNextStep();
      }, 600);
      return () => clearTimeout(timer);
    }
  }, [showNextStep, handleNextStep]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleDecode();
  };

  const handleChange = (field: string, value: string) => {
    setVinData({ [field]: value });
  };

  return (
    <form className={styles.formContainer} onSubmit={handleSubmit}>
      <h1 className={styles.title}>Revolutionary Vehicle Service Contract</h1>
      <p className={styles.subtitle}>Use the fields below to enter and validate your VIN, and vehicle odometer.</p>
      
      <div className={styles.demoCheckbox}>
        <label className={styles.demoLabel}>
          <input
            type="checkbox"
            checked={isDemoMode}
            onChange={(e) => handleDemoToggle(e.target.checked)}
            className={styles.demoInput}
          />
          <span className={styles.demoText}>Demo Mode (Auto-fill Honda Accord 2003)</span>
        </label>
      </div>
      
      <div className={styles.formRow}>
        <div className={styles.inputGroup}>
          <label htmlFor="input_98_36" className={styles.label}>VIN Number</label>
          <input
            id="input_98_36"
            name="input_36"
            type="text"
            value={vinData.input_36}
            onChange={(e) => handleChange('input_36', e.target.value)}
            className={styles.input}
            placeholder="Enter 17-character VIN"
            maxLength={17}
            required
          />
          {errors.input_36 && <div className={styles.error}>{errors.input_36}</div>}
        </div>

        <div className={styles.inputGroup}>
          <label htmlFor="input_98_138" className={styles.label}>Odometer Reading</label>
          <input
            id="input_98_138"
            name="input_138"
            type="number"
            value={vinData.input_138}
            onChange={(e) => handleChange('input_138', e.target.value)}
            className={styles.input}
            placeholder="Enter mileage"
            min="0"
            max="999999"
            required
          />
          {errors.input_138 && <div className={styles.error}>{errors.input_138}</div>}
        </div>
      </div>
      
      <div className={styles.buttonRow}>
        <button 
          type="submit" 
          className={`shared-button primary ${loading ? 'loading' : ''}`} 
          disabled={loading}
        >
          {loading ? 'Processing...' : 'Decode VIN & Continue'}
        </button>
      </div>
      
      {loading && <div className={styles.loading}>Checking VIN...</div>}
      {errors.general && <div className={styles.error}>{errors.general}</div>}
    </form>
  );
};

export default VinOdometerForm; 
