// Base step definitions
export const STEP_DEFINITIONS = {
  order: {
    id: 'order',
    label: 'Build a Contract',
    description: 'Order Form',
    completed: false,
    current: false,
    onClick: undefined
  },
  recommend: {
    id: 'recommend',
    label: 'Our Recommendation',
    description: 'Recommend',
    completed: false,
    current: false,
    onClick: undefined
  },
  'adjust-limit': {
    id: 'adjust-limit',
    label: 'Adjust Limit of Liability',
    description: 'Adjust Limit',
    completed: false,
    current: false,
    onClick: undefined
  },
  summary: {
    id: 'summary',
    label: 'Order Summary/Sign and Submit',
    description: 'Order Summary',
    completed: false,
    current: false,
    onClick: undefined
  }
};

// Flow types for different navigation paths
export type FlowType = 'direct' | 'direct-with-adjust' | 'recommend' | 'recommend-with-adjust' | 'price-match' | 'price-match-with-adjust';

// Dynamic step generation based on flow type
export const generateStepsForFlow = (flowType: FlowType) => {
  const steps = [];

  switch (flowType) {
    case 'direct':
      // Tabs 1-3 without Low Limit: Order → Summary
      steps.push(STEP_DEFINITIONS.order, STEP_DEFINITIONS.summary);
      break;

    case 'direct-with-adjust':
      // Tabs 1-3 with Low Limit: Order → Adjust Limit → Summary
      steps.push(STEP_DEFINITIONS.order, STEP_DEFINITIONS['adjust-limit'], STEP_DEFINITIONS.summary);
      break;

    case 'recommend':
      // Tab 4 without Low Limit: Order → Summary
      steps.push(STEP_DEFINITIONS.order, STEP_DEFINITIONS.summary);
      break;

    case 'recommend-with-adjust':
      // Tab 4 with Low Limit: Order → Adjust Limit → Summary
      steps.push(STEP_DEFINITIONS.order, STEP_DEFINITIONS['adjust-limit'], STEP_DEFINITIONS.summary);
      break;

    case 'price-match':
      // Tab 5 without Low Limit: Order → Summary
      steps.push(STEP_DEFINITIONS.order, STEP_DEFINITIONS.summary);
      break;

    case 'price-match-with-adjust':
      // Tab 5 with Low Limit: Order → Adjust Limit → Summary
      steps.push(STEP_DEFINITIONS.order, STEP_DEFINITIONS['adjust-limit'], STEP_DEFINITIONS.summary);
      break;

    default:
      // Fallback to basic flow
      steps.push(STEP_DEFINITIONS.order, STEP_DEFINITIONS.summary);
  }

  return steps.map(step => ({ ...step })); // Return deep copies
};

// Helper function to determine flow type based on selected option and low limit state
export const getFlowType = (selectedOption: string, hasLowLimit: boolean): FlowType => {
  switch (selectedOption) {
    case 'option1':
    case 'option2':
    case 'option3':
      return hasLowLimit ? 'direct-with-adjust' : 'direct';

    case 'option4':
      return hasLowLimit ? 'recommend-with-adjust' : 'recommend';

    case 'option5':
      return hasLowLimit ? 'price-match-with-adjust' : 'price-match';

    default:
      return 'direct';
  }
};

// Legacy exports for backward compatibility
export const CONTRACT_STEPS = generateStepsForFlow('direct-with-adjust');
export const CONTRACT_STEPS_WITHOUT_ADJUST = generateStepsForFlow('direct');
