/* Global styles using design system */

:root {
  font-family: var(--font-family-primary);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
  color-scheme: light dark;
  color: var(--color-gray-800);
  background-color: var(--color-white);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: var(--font-weight-medium);
  color: var(--color-secondary);
  text-decoration: inherit;
}

a:hover {
  color: var(--color-secondary-dark);
}

/* Chrome, Edge, Safari */
::-webkit-scrollbar {
  width: 12px;
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #2563eb;
  border-radius: 6px;
}
::-webkit-scrollbar-thumb:hover {
  background: #174ea6;
}

/* Firefox */
html {
  scrollbar-width: thin;
  scrollbar-color: #2563eb #f1f1f1;
}

h1 {
  font-size: var(--font-size-3xl);
  line-height: var(--line-height-tight);
}

button {
  border-radius: var(--radius-lg);
  border: 1px solid transparent;
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  font-family: inherit;
  background-color: var(--color-gray-800);
  cursor: pointer;
  transition: border-color var(--transition-normal);
}

#vin-form button {
  white-space: wrap;
}

button:hover {
  border-color: var(--color-secondary);
}

button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.lower-limit-active:focus,
.lower-limit-active:focus-visible {
  border-color: #dc2626 !important;
  outline: none !important;
}

@media (prefers-color-scheme: light) {
  :root {
    color: var(--color-gray-800);
    background-color: var(--color-white);
  }
  
  a:hover {
    color: var(--color-secondary-dark);
  }
  
  button {
    background-color: var(--color-gray-100);
  }
}
