import React from 'react';

interface Step {
  id: string;
  label: string;
  description: string;
  completed: boolean;
  current: boolean;
  onClick?: () => void;
}

interface StepBarProps {
  steps: Step[];
  currentStep: string;
  onStepClick?: (stepId: string) => void;
  allowNavigation?: boolean;
}

const StepBar: React.FC<StepBarProps> = ({
  steps,
  currentStep,
  onStepClick,
  allowNavigation = true
}) => {
  const isMobile = window.innerWidth <= 768;
  
  const handleStepClick = (stepId: string) => {
    if (allowNavigation && onStepClick) {
      onStepClick(stepId);
    }
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: isMobile ? 'column' : 'row',
      alignItems: isMobile ? 'flex-start' : 'center',
      justifyContent: isMobile ? 'flex-start' : 'center',
      marginBottom: '2rem',
      padding: isMobile ? '1rem 0.5rem' : '1rem',
      background: '#f8fafc',
      border: '1px solid #e5e7eb',
      borderRadius: 12,
      boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
      gap: isMobile ? '0.5rem' : '0'
    }}>
      {steps.map((step, index) => {
        const currentIndex = steps.findIndex(s => s.id === currentStep);
        const isCompleted = index < currentIndex;
        const isCurrent = currentStep === step.id;
        const isClickable = allowNavigation && onStepClick;
        
        return (
          <React.Fragment key={step.id}>
            <div
              onClick={() => handleStepClick(step.id)}
              style={{
                display: 'flex',
                flexDirection: isMobile ? 'row' : 'column',
                alignItems: 'center',
                cursor: isClickable ? 'pointer' : 'default',
                padding: isMobile ? '0.5rem' : '0.5rem',
                borderRadius: 8,
                transition: 'all 0.2s ease',
                width: isMobile ? '100%' : 'auto',
                gap: isMobile ? '0.75rem' : '0'
              }}
            >
              {/* Step Circle */}
              <div style={{
                width: isMobile ? 32 : 40,
                height: isMobile ? 32 : 40,
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: isCurrent 
                  ? '#2563eb'
                  : isCompleted
                  ? '#16a34a'
                  : '#e5e7eb',
                color: isCurrent || isCompleted ? '#fff' : '#64748b',
                fontWeight: 600,
                fontSize: isMobile ? 14 : 16,
                transition: 'all 0.2s ease',
                flexShrink: 0
              }}>
                {isCompleted ? '✓' : index + 1}
              </div>
              
              {/* Step Label */}
              <div style={{
                marginTop: isMobile ? 0 : 8,
                textAlign: isMobile ? 'left' : 'center',
                fontSize: isMobile ? 14 : 14,
                fontWeight: isCurrent ? 600 : 500,
                color: isCurrent ? '#2563eb' : isCompleted ? '#16a34a' : '#64748b',
                flex: isMobile ? 1 : 'none'
              }}>
                {step.label}
              </div>
            </div>

            {/* Connector Line */}
            {index < steps.length - 1 && (
              <div style={{
                width: isMobile ? 2 : 60,
                height: isMobile ? 20 : 2,
                background: isCompleted ? '#16a34a' : '#e5e7eb',
                margin: isMobile ? '0 15px' : '0 1rem',
                borderRadius: 1,
                transition: 'background 0.3s ease'
              }} />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default StepBar; 
