import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  className?: string;
}

const baseStyle: React.CSSProperties = {
  border: '2px solid #dc2626',
  color: '#fff',
  background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
  borderRadius: 10,
  padding: '0.75rem 2rem',
  fontWeight: 700,
  fontSize: 16,
  fontFamily: `'Inter', 'Segoe UI', 'system-ui', Arial, sans-serif`,
  cursor: 'pointer',
  boxShadow: '0 2px 8px rgba(220,38,38,0.08)',
  transition: 'all 0.2s',
  outline: 'none',
  minWidth: 120,
  margin: 0
};

const Button: React.FC<ButtonProps> = ({ children, className, style, ...props }) => (
  <button
    style={{ ...baseStyle, ...style }}
    className={className}
    {...props}
  >
    {children}
  </button>
);

export default Button; 