.container {
  max-width: 980px;
  margin: 2rem auto;
  padding: 2.5rem 2rem;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  background: #fff;
  border: 1px solid #e5e7eb;
  font-family: 'Inter', 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

.formCard {
  background: #fff;
  border-radius: 12px;
  padding: 2rem;
  margin-top: 1.5rem;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.header p {
  color: #6b7280;
  font-size: 0.95rem;
}

.sliderSection {
  margin: 2rem 0;
  padding: 1rem 0;
}

.slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #dc2626;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #dc2626;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.detailsSection {
  background: #f9fafb;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.detailRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.detailRow:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #374151;
  font-size: 0.95rem;
}

.value {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.95rem;
}

.buttonGroup {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.priceInputContainer {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.priceInputContainer:focus-within {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.priceInputContainer.focused {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
  transform: translateY(-1px);
}

.priceInputContainer:hover {
  border-color: #9ca3af;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.currencySymbol {
  background: #f8fafc;
  color: #64748b;
  padding: 0.5rem 0.75rem;
  font-weight: 600;
  font-size: 0.95rem;
  border-right: 1px solid #e2e8f0;
}

.priceInput {
  background: transparent;
  border: none;
  padding: 0.5rem 0.75rem;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1f2937;
  width: 100px;
  text-align: right;
  outline: none;
  font-family: 'Inter', monospace;
}

.priceInput::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.priceInput:focus {
  color: #1f2937;
}

.priceInput:focus::placeholder {
  color: #d1d5db;
}

/* Enhanced label styling for better readability */
.detailRow .label {
  transition: color 0.2s ease;
}

.detailRow:hover .label {
  color: #1f2937;
}

@media (max-width: 768px) {
  .priceInputContainer {
    width: 140px;
  }
  
  .priceInput {
    width: 80px;
    font-size: 0.875rem;
  }
  
  .currencySymbol {
    font-size: 0.875rem;
    padding: 0.4rem 0.6rem;
  }
}

@media (max-width: 480px) {
  .detailRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .priceInputContainer {
    width: 100%;
    max-width: 160px;
  }
  
  .priceInput {
    width: 100%;
  }
}

/* Mobile Responsive - 768px and below */
@media (max-width: 768px) {
  .container {
    width: 100vw;
    max-width: 100vw;
    margin: 0;
    padding: 1rem 0.5rem;
    border-radius: 0;
    box-sizing: border-box;
  }
  
  .formCard {
    padding: 1.5rem 1rem;
    margin-top: 1rem;
    border-radius: 8px;
  }
  
  .header h2 {
    font-size: 1.25rem;
  }
  
  .header p {
    font-size: 0.875rem;
    padding: 0 0.5rem;
  }
  
  .sliderSection {
    margin: 1.5rem 0;
    padding: 0.75rem 0;
  }
  
  .detailsSection {
    padding: 1rem;
    margin: 1rem 0;
  }
  
  .detailRow {
    padding: 0.75rem 0;
    flex-wrap: wrap;
    gap: 0.25rem;
  }
  
  .label {
    font-size: 0.875rem;
    flex: 1;
    min-width: 120px;
  }
  
  .value {
    font-size: 0.875rem;
    text-align: right;
    flex: 1;
  }
  
  .buttonGroup {
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1.5rem;
  }
  
  .buttonGroup button {
    width: 100%;
    min-width: auto;
  }
}

/* Small Mobile - 480px and below */
@media (max-width: 480px) {
  .container {
    padding: 0.75rem 0.25rem;
  }
  
  .formCard {
    padding: 1rem 0.75rem;
  }
  
  .header h2 {
    font-size: 1.125rem;
  }
  
  .header p {
    font-size: 0.8rem;
  }
  
  .detailsSection {
    padding: 0.75rem;
  }
  
  .detailRow {
    padding: 0.5rem 0;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .label {
    font-size: 0.8rem;
    width: 100%;
  }
  
  .value {
    font-size: 0.875rem;
    font-weight: 700;
    width: 100%;
    text-align: left;
  }
  
  .slider {
    height: 10px;
  }
  
  .slider::-webkit-slider-thumb {
    width: 24px;
    height: 24px;
  }
  
  .slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
  }
}

/* Large Mobile/Tablet - 600px to 800px */
@media (min-width: 481px) and (max-width: 800px) {
  .container {
    width: 95vw;
    margin: 1rem auto;
    padding: 1.5rem 1rem;
    border-radius: 12px;
  }
  
  .detailRow {
    padding: 0.625rem 0;
  }
  
  .buttonGroup {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .buttonGroup button {
    flex: 1;
    min-width: 140px;
    max-width: 200px;
  }
}

/* Low Limit Warning Styles */
.lowLimitWarning {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.15);
  animation: slideIn 0.3s ease-out;
}

.warningIcon {
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.warningContent {
  flex: 1;
}

.warningTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: #92400e;
  margin-bottom: 0.25rem;
}

.warningText {
  font-size: 0.8rem;
  color: #a16207;
  line-height: 1.4;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Clean Values Row Layout - Desktop Optimized */
.valuesRow {
  display: flex;
  align-items: stretch;
  gap: 2rem;
  margin-bottom: 1.5rem;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
}

.valueItem {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-width: 0;
  max-width: 50%; /* Ensure equal width distribution */
}

.fieldLabel {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  display: block;
}

.fieldDivider {
  width: 1px;
  height: 60px;
  background: #e5e7eb;
  margin-top: 1.5rem;
  flex-shrink: 0;
}

.fieldContainer {
  display: flex;
  align-items: center;
  background: #ffffff;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  overflow: hidden;
  transition: border-color 0.2s ease;
  height: 52px;
  width: 100%;
  box-sizing: border-box;
  min-width: 200px; /* Minimum width for desktop */
  max-width: 100%; /* Don't exceed container */
}

.fieldContainer:hover {
  border-color: #3b82f6;
}

.fieldContainer:focus-within {
  border-color: #1d4ed8;
  box-shadow: 0 0 0 3px rgba(29, 78, 216, 0.1);
}

.dollarSign {
  background: #f1f5f9;
  color: #64748b;
  padding: 0 1rem;
  font-weight: 600;
  font-size: 16px;
  height: 100%;
  display: flex;
  align-items: center;
  border-right: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.fieldInput {
  flex: 1;
  border: none;
  outline: none;
  padding: 0 1rem;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  background: transparent;
  text-align: right;
  height: 100%;
}

.fieldDisplay {
  flex: 1;
  padding: 0 1rem;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  text-align: right;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* Desktop-specific enhancements (769px and above) */
@media (min-width: 769px) {
  .valuesRow {
    align-items: stretch;
    min-height: 80px; /* Ensure consistent height */
  }

  .valueItem {
    flex: 1 1 50%; /* Equal flex basis */
    min-width: 250px; /* Minimum width for readability */
    max-width: 50%; /* Strict 50% limit */
  }

  .fieldContainer {
    min-width: 250px; /* Ensure adequate width */
    height: 52px;
  }

  .fieldDivider {
    height: 70px;
    margin-top: 1rem;
    align-self: center;
  }

  .dollarSign {
    min-width: 40px; /* Consistent dollar sign width */
    justify-content: center;
  }

  .fieldInput,
  .fieldDisplay {
    min-width: 150px; /* Ensure adequate input/display area */
  }
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .lowLimitWarning {
    padding: 0.75rem;
    gap: 0.5rem;
  }

  .warningIcon {
    font-size: 1rem;
  }

  .warningTitle {
    font-size: 0.8rem;
  }

  .warningText {
    font-size: 0.75rem;
  }

  .valuesRow {
    gap: 1rem;
    padding: 1rem;
    align-items: stretch; /* Ensure equal height containers */
  }

  .valueItem {
    gap: 0.5rem;
    flex: 1; /* Equal width for both items */
    min-width: 0; /* Prevent overflow */
  }

  .fieldLabel {
    font-size: 12px;
    margin-bottom: 0.25rem;
  }

  .fieldDivider {
    height: 50px;
    margin-top: 1rem;
    flex-shrink: 0; /* Don't shrink the divider */
  }

  .fieldContainer {
    height: 48px;
    width: 100%; /* Force full width */
    min-width: 0; /* Prevent overflow */
    flex-shrink: 0; /* Don't shrink */
  }

  .dollarSign {
    padding: 0 0.75rem;
    font-size: 14px;
    white-space: nowrap; /* Prevent wrapping */
  }

  .fieldInput,
  .fieldDisplay {
    padding: 0 0.75rem;
    font-size: 14px;
    min-width: 0; /* Allow shrinking if needed */
  }
}

@media (max-width: 480px) {
  .valuesRow {
    gap: 0.75rem;
    padding: 0.75rem;
    align-items: stretch;
  }

  .valueItem {
    flex: 1;
    min-width: 0;
  }

  .fieldLabel {
    font-size: 11px;
  }

  .fieldContainer {
    height: 44px;
    width: 100%;
    min-width: 0;
  }

  .dollarSign {
    padding: 0 0.5rem;
    font-size: 13px;
    white-space: nowrap;
  }

  .fieldInput,
  .fieldDisplay {
    padding: 0 0.5rem;
    font-size: 13px;
    min-width: 0;
  }

  .fieldDivider {
    height: 45px;
    flex-shrink: 0;
  }
}

/* Enhanced Slider Tooltip Styles */
.sliderContainer {
  position: relative;
  width: 100%;
  padding: 0.5rem 0;
}

.sliderTooltip {
  position: absolute;
  top: -75px;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #1f2937, #374151);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  z-index: 20;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: tooltipFadeIn 0.3s ease-out;
  backdrop-filter: blur(10px);
}

.tooltipContent {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.tooltipLabel {
  font-size: 0.7rem;
  color: #d1d5db;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.tooltipValue {
  font-size: 1rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.tooltipArrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #1f2937;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
}

/* Enhanced Slider Styling */
.slider {
  width: 100%;
  height: 10px;
  border-radius: 5px;
  background: linear-gradient(to right, #e5e7eb, #d1d5db);
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.slider:hover {
  background: linear-gradient(to right, #d1d5db, #9ca3af);
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  cursor: pointer;
  border: 3px solid #ffffff;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  cursor: pointer;
  border: 3px solid #ffffff;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}

/* Old styles removed - using clean new implementation above */


