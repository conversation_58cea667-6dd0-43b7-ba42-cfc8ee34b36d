/* Optimized Input Styles using Design System */
/* Base Input Styles */
.shared-input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  background-color: var(--color-white);
  color: var(--color-gray-800);
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-medium);
  box-shadow: var(--shadow-sm);
  outline: none;
}

.shared-input:focus {
  border-color: var(--color-primary);
  background-color: var(--color-white);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1), var(--shadow-md);
  transform: translateY(-1px);
}

.shared-input:hover {
  border-color: var(--color-gray-300);
  box-shadow: var(--shadow-md);
}

.shared-input:disabled {
  background-color: var(--color-gray-50);
  color: var(--color-gray-900);
  cursor: not-allowed;
}

.shared-input::placeholder {
  color: var(--color-gray-400);
  opacity: 1;
}

/* Input with Icon Container */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-icon .shared-input {
  padding-left: 3rem;
}

.input-with-icon .input-icon {
  position: absolute;
  left: var(--spacing-4);
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: var(--color-gray-500);
  z-index: 2;
  transition: color var(--transition-normal);
}



.input-with-icon .shared-input:focus + .input-icon {
  color: var(--color-primary);
}

.input-with-icon .shared-input:hover + .input-icon {
  color: var(--color-gray-600);
}

/* Special styling for VIN input */
.shared-input.vin-input {
  font-family: var(--font-family-mono);
  letter-spacing: 0.1em;
  font-weight: var(--font-weight-semibold);
}

/* Label Styles */
.shared-label {
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-2);
  color: var(--color-gray-700);
  font-size: var(--font-size-xs);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-family: var(--font-family-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.shared-label::before {
  content: '';
  width: 3px;
  height: 12px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  border-radius: 2px;
}

/* Alternative label style for order form */
.shared-label-alt {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-base);
  font-family: var(--font-family-primary);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-1);
  display: block;
}

/* Input Group Container */
.input-group {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--spacing-4);
}

/* Grid Layout for Multiple Inputs */
.input-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

/* Custom Radio and Checkbox Styles */
.custom-radio-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-800);
  cursor: pointer;
  padding: var(--spacing-1) 0;
}

.custom-radio {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid var(--color-gray-300);
  border-radius: 50%;
  background: var(--color-white);
  outline: none;
  transition: border-color var(--transition-normal);
  position: relative;
  cursor: pointer;
  margin: 0;
}

.custom-radio:checked {
  border-color: var(--color-secondary);
}

.custom-radio:checked::before {
  content: '';
  display: block;
  width: 10px;
  height: 10px;
  background: var(--color-secondary);
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
}

.custom-radio:focus {
  border-color: var(--color-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .shared-input {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
  }
  
  .input-with-icon .shared-input {
    padding-left: 2.5rem;
  }
  
  .input-with-icon .input-icon {
    left: var(--spacing-3);
    width: 1rem;
    height: 1rem;
  }
  
  .shared-label {
    font-size: var(--font-size-xs);
  }
  
  .shared-label-alt {
    font-size: var(--font-size-sm);
  }
  
  .input-grid {
    grid-template-columns: 1fr;
  }
}

/* Icon positioning utilities */
.input-icon-right {
  position: absolute;
  right: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
} 