import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import StepBar from '../../components/StepBar';
import { useStepContext } from '../../contexts/StepContext';

import styles from './AdjustLimitForm.module.css';
import { validateNumericInput, formatCurrencyInput, removeCurrencyFormatting } from '../../utils/validation';

interface AdjustLimitFormProps {
  onBack: () => void;
  onNext: () => void;
  initialData?: {
    coverage: string;
    term: string;
    deductible: number;
    basePrice: number;
    limitOfLiability: number;
    dealerMarkup: number;
  };
}

const AdjustLimitForm: React.FC<AdjustLimitFormProps> = ({
  onBack,
  onNext,
  initialData
}) => {
  const { currentStepId, goToStep, getCurrentSteps } = useStepContext();

  const [adjustedPrice, setAdjustedPrice] = useState(initialData?.basePrice || 1477.00);
  const [limitOfLiability, setLimitOfLiability] = useState(initialData?.limitOfLiability || 10000.00);
  const [dealerMarkup, setDealerMarkup] = useState(initialData?.dealerMarkup || 178.00);

  const [priceValue, setPriceValue] = useState(formatCurrencyInput(adjustedPrice.toString()));

  // Tooltip states for slider
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState(0);

  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = validateNumericInput(e.target.value, true);
    setPriceValue(value);
    const numValue = parseFloat(value) || 0;
    setAdjustedPrice(numValue);
    // Calculate corresponding limit of liability and dealer markup
    const ratio = numValue / (initialData?.basePrice || 1477.00);
    setLimitOfLiability((initialData?.limitOfLiability || 10000.00) * ratio);
    setDealerMarkup((initialData?.dealerMarkup || 178.00) * ratio);
  };

  const handlePriceBlur = () => {
    if (priceValue) {
      const formatted = formatCurrencyInput(priceValue);
      setPriceValue(formatted);
    }
  };

  const handlePriceFocus = () => {
    const numericValue = removeCurrencyFormatting(priceValue);
    setPriceValue(numericValue);
  };

  const handleSliderChange = (value: number) => {
    setAdjustedPrice(value);
    // Update the price input field to reflect slider changes
    setPriceValue(formatCurrencyInput(value.toString()));
    // Calculate corresponding limit of liability and dealer markup
    const ratio = value / (initialData?.basePrice || 1477.00);
    setLimitOfLiability((initialData?.limitOfLiability || 10000.00) * ratio);
    setDealerMarkup((initialData?.dealerMarkup || 178.00) * ratio);
  };

  const handleSaveAndContinue = () => {
    // Save the adjusted values
    onNext();
  };


  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <div className={styles.container}>
      <button
        className="shared-button secondary back-button"
        onClick={onBack}
        style={{ marginBottom: 18, fontSize: 15, padding: '0.5rem 1.3rem' }}
      >
        ← Back
      </button>

      <StepBar
        steps={getCurrentSteps()}
        currentStep={currentStepId}
        onStepClick={goToStep}
        allowNavigation={true}
      />

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className={styles.formCard}
      >
        <div className={styles.header}>
          <h2>Adjust Limit of Liability</h2>
          <p>Move the Slider to Adjust the price and the Limit of Liability of the contract.</p>
        </div>

        <div className={styles.sliderSection}>
          <div className={styles.sliderContainer}>
            <input
              type="range"
              min="1000"
              max="5000"
              step="10"
              value={adjustedPrice}
              onChange={(e) => handleSliderChange(Number(e.target.value))}
              onMouseDown={() => {
                setShowTooltip(true);
                const percentage = ((adjustedPrice - 1000) / (5000 - 1000)) * 100;
                setTooltipPosition(percentage);
              }}
              onMouseUp={() => setShowTooltip(false)}
              onMouseMove={() => {
                if (showTooltip) {
                  const percentage = ((adjustedPrice - 1000) / (5000 - 1000)) * 100;
                  setTooltipPosition(percentage);
                }
              }}
              className={styles.slider}
            />

            {/* Tooltip for Limit of Liability */}
            {showTooltip && (
              <div
                className={styles.sliderTooltip}
                style={{ left: `${tooltipPosition}%` }}
              >
                <div className={styles.tooltipContent}>
                  <div className={styles.tooltipLabel}>Limit of Liability</div>
                  <div className={styles.tooltipValue}>${limitOfLiability.toFixed(2)}</div>
                </div>
                <div className={styles.tooltipArrow}></div>
              </div>
            )}
          </div>

          {/* Low Limit Warning */}
          {limitOfLiability < 8000 && (
            <div className={styles.lowLimitWarning}>
              <div className={styles.warningIcon}>⚠️</div>
              <div className={styles.warningContent}>
                <div className={styles.warningTitle}>Low Limit of Liability</div>
                <div className={styles.warningText}>
                  Current limit: ${limitOfLiability.toFixed(2)}. Consider increasing for better coverage.
                </div>
              </div>
            </div>
          )}
        </div>

        <div className={styles.detailsSection}>
          <div className={styles.valuesRow}>
            <div className={styles.valueItem}>
              <label className={styles.fieldLabel}>Adjusted Price:</label>
              <div className={styles.fieldContainer}>
                <span className={styles.dollarSign}>$</span>
                <input
                  type="text"
                  value={priceValue}
                  onChange={handlePriceChange}
                  onFocus={handlePriceFocus}
                  onBlur={handlePriceBlur}
                  className={styles.fieldInput}
                  placeholder="0.00"
                />
              </div>
            </div>

            <div className={styles.fieldDivider}></div>

            <div className={styles.valueItem}>
              <label className={styles.fieldLabel}>Limit of Liability ($):</label>
              <div className={styles.fieldContainer}>
                <span className={styles.dollarSign}>$</span>
                <div className={styles.fieldDisplay}>
                  {limitOfLiability.toFixed(2)}
                </div>
              </div>
            </div>
          </div>
          <div className={styles.detailRow}>
            <span className={styles.label}>Coverage:</span>
            <span className={styles.value}>Essential</span>
          </div>
          <div className={styles.detailRow}>
            <span className={styles.label}>Term:</span>
            <span className={styles.value}>12 Month, Unlimited Mile</span>
          </div>
          <div className={styles.detailRow}>
            <span className={styles.label}>Deductible:</span>
            <span className={styles.value}>${initialData?.deductible || 200}</span>
          </div>

          {/* Hidden - kept for internal calculations but not displayed to user */}
          {/* <div className={styles.detailRow}>
            <span className={styles.label}>Adjusted Dealer Markup:</span>
            <span className={styles.value}>${dealerMarkup.toFixed(2)}</span>
          </div> */}
        </div>

        <div className={styles.buttonGroup}>
          <button
            type="button"
            className="shared-button secondary"
            onClick={onBack}
          >
            Previous
          </button>
          <button
            type="button"
            className="shared-button primary"
            onClick={handleSaveAndContinue}
          >
            Next
          </button>
          <button
            type="button"
            className="shared-button secondary"
            onClick={handleSaveAndContinue}
          >
            Save & Continue
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default AdjustLimitForm;






