import React from 'react';

const CarBackground: React.FC = () => (
  <div className="car-silhouette">
    <svg viewBox="0 0 500 250" className="car-svg" xmlns="http://www.w3.org/2000/svg">
      {/* Car Body */}
      <defs>
        <filter id="glow">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge> 
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
        
        <filter id="shadow">
          <feDropShadow dx="0" dy="4" stdDeviation="8" floodColor="rgba(0,0,0,0.3)"/>
        </filter>
      </defs>
      
      {/* Car Shadow */}
      <ellipse cx="250" cy="200" rx="180" ry="20" 
               fill="rgba(0,0,0,0.15)" 
               className="car-shadow"/>
      
      {/* Main Car Body - Sports Car Style */}
      <path d="M 60 160 Q 50 160 45 155 Q 40 150 40 145 Q 40 140 45 135 Q 50 130 60 130 L 440 130 Q 450 130 455 135 Q 460 140 460 145 Q 460 150 455 155 Q 450 160 440 160 Z" 
            fill="rgba(255,255,255,0.2)" 
            stroke="rgba(255,255,255,0.15)" 
            strokeWidth="1.5"
            filter="url(#shadow)"
            className="car-body"/>
      
      {/* Car Hood */}
      <path d="M 60 130 Q 50 130 45 125 Q 40 120 40 115 Q 40 110 45 105 Q 50 100 60 100 L 440 100 Q 450 100 455 105 Q 460 110 460 115 Q 460 120 455 125 Q 450 130 440 130 Z" 
            fill="rgba(255,255,255,0.18)" 
            stroke="rgba(255,255,255,0.12)" 
            strokeWidth="1"
            className="car-hood"/>
      
      {/* Car Roof - Convertible Style */}
      <path d="M 120 100 Q 110 100 105 95 Q 100 90 100 85 Q 100 80 105 75 Q 110 70 120 70 L 380 70 Q 390 70 395 75 Q 400 80 400 85 Q 400 90 395 95 Q 390 100 380 100 Z" 
            fill="rgba(255,255,255,0.15)" 
            stroke="rgba(255,255,255,0.1)" 
            strokeWidth="1"
            className="car-roof"/>
      
      {/* Front Windshield */}
      <path d="M 120 100 Q 115 100 112 97 Q 110 95 110 92 Q 110 90 112 87 Q 115 85 120 85 L 180 85 Q 185 85 188 87 Q 190 90 190 92 Q 190 95 188 97 Q 185 100 180 100 Z" 
            fill="rgba(255,255,255,0.3)" 
            stroke="rgba(255,255,255,0.2)" 
            strokeWidth="1"
            className="car-window"/>
      
      {/* Back Windshield */}
      <path d="M 320 100 Q 315 100 312 97 Q 310 95 310 92 Q 310 90 312 87 Q 315 85 320 85 L 380 85 Q 385 85 388 87 Q 390 90 390 92 Q 390 95 388 97 Q 385 100 380 100 Z" 
            fill="rgba(255,255,255,0.3)" 
            stroke="rgba(255,255,255,0.2)" 
            strokeWidth="1"
            className="car-window"/>
      
      {/* Front Wheel - Detailed */}
      <circle cx="140" cy="160" r="20" 
              fill="rgba(255,255,255,0.25)" 
              stroke="rgba(255,255,255,0.3)" 
              strokeWidth="2"
              className="car-wheel"/>
      <circle cx="140" cy="160" r="12" 
              fill="rgba(255,255,255,0.15)" 
              stroke="rgba(255,255,255,0.4)" 
              strokeWidth="1.5"/>
      <circle cx="140" cy="160" r="6" 
              fill="rgba(255,255,255,0.1)" 
              stroke="rgba(255,255,255,0.5)" 
              strokeWidth="1"/>
      
      {/* Back Wheel - Detailed */}
      <circle cx="360" cy="160" r="20" 
              fill="rgba(255,255,255,0.25)" 
              stroke="rgba(255,255,255,0.3)" 
              strokeWidth="2"
              className="car-wheel"/>
      <circle cx="360" cy="160" r="12" 
              fill="rgba(255,255,255,0.15)" 
              stroke="rgba(255,255,255,0.4)" 
              strokeWidth="1.5"/>
      <circle cx="360" cy="160" r="6" 
              fill="rgba(255,255,255,0.1)" 
              stroke="rgba(255,255,255,0.5)" 
              strokeWidth="1"/>
      
      {/* Front Headlight - LED Style */}
      <ellipse cx="80" cy="120" rx="12" ry="8" 
               fill="rgba(255,255,255,0.5)" 
               filter="url(#glow)"
               className="car-headlight"/>
      <ellipse cx="80" cy="120" rx="6" ry="4" 
               fill="rgba(255,255,255,0.8)" 
               filter="url(#glow)"/>
      
      {/* Back Headlight - LED Style */}
      <ellipse cx="420" cy="120" rx="12" ry="8" 
               fill="rgba(255,255,255,0.5)" 
               filter="url(#glow)"
               className="car-headlight"/>
      <ellipse cx="420" cy="120" rx="6" ry="4" 
               fill="rgba(255,255,255,0.8)" 
               filter="url(#glow)"/>
      
      {/* Front Taillight */}
      <ellipse cx="85" cy="125" rx="8" ry="5" 
               fill="rgba(220,38,38,0.6)" 
               filter="url(#glow)"
               className="car-taillight"/>
      
      {/* Back Taillight */}
      <ellipse cx="415" cy="125" rx="8" ry="5" 
               fill="rgba(220,38,38,0.6)" 
               filter="url(#glow)"
               className="car-taillight"/>
      
      {/* Front Grill - Sporty */}
      <rect x="70" y="110" width="25" height="15" 
            fill="none" 
            stroke="rgba(255,255,255,0.2)" 
            strokeWidth="1"
            className="car-grill"/>
      <line x1="72" y1="112" x2="93" y2="112" stroke="rgba(255,255,255,0.15)" strokeWidth="0.8"/>
      <line x1="72" y1="115" x2="93" y2="115" stroke="rgba(255,255,255,0.15)" strokeWidth="0.8"/>
      <line x1="72" y1="118" x2="93" y2="118" stroke="rgba(255,255,255,0.15)" strokeWidth="0.8"/>
      <line x1="72" y1="121" x2="93" y2="121" stroke="rgba(255,255,255,0.15)" strokeWidth="0.8"/>
      
      {/* Back Grill - Sporty */}
      <rect x="405" y="110" width="25" height="15" 
            fill="none" 
            stroke="rgba(255,255,255,0.2)" 
            strokeWidth="1"
            className="car-grill"/>
      <line x1="407" y1="112" x2="428" y2="112" stroke="rgba(255,255,255,0.15)" strokeWidth="0.8"/>
      <line x1="407" y1="115" x2="428" y2="115" stroke="rgba(255,255,255,0.15)" strokeWidth="0.8"/>
      <line x1="407" y1="118" x2="428" y2="118" stroke="rgba(255,255,255,0.15)" strokeWidth="0.8"/>
      <line x1="407" y1="121" x2="428" y2="121" stroke="rgba(255,255,255,0.15)" strokeWidth="0.8"/>
      
      {/* Front Door Handle */}
      <rect x="160" y="110" width="4" height="12" 
            fill="rgba(255,255,255,0.25)" 
            stroke="rgba(255,255,255,0.4)" 
            strokeWidth="0.8"
            rx="2"
            className="car-door-handle"/>
      
      {/* Back Door Handle */}
      <rect x="336" y="110" width="4" height="12" 
            fill="rgba(255,255,255,0.25)" 
            stroke="rgba(255,255,255,0.4)" 
            strokeWidth="0.8"
            rx="2"
            className="car-door-handle"/>
      
      {/* Front Side Mirror - Sporty */}
      <ellipse cx="100" cy="95" rx="8" ry="5" 
               fill="rgba(255,255,255,0.2)" 
               stroke="rgba(255,255,255,0.3)" 
               strokeWidth="1"
               className="car-mirror"/>
      
      {/* Back Side Mirror - Sporty */}
      <ellipse cx="400" cy="95" rx="8" ry="5" 
               fill="rgba(255,255,255,0.2)" 
               stroke="rgba(255,255,255,0.3)" 
               strokeWidth="1"
               className="car-mirror"/>
      
      {/* Exhaust Pipes */}
      <rect x="70" y="145" width="3" height="8" 
            fill="rgba(100,100,100,0.4)" 
            stroke="rgba(255,255,255,0.2)" 
            strokeWidth="0.5"
            rx="1"
            className="car-exhaust"/>
      <rect x="427" y="145" width="3" height="8" 
            fill="rgba(100,100,100,0.4)" 
            stroke="rgba(255,255,255,0.2)" 
            strokeWidth="0.5"
            rx="1"
            className="car-exhaust"/>
      
      {/* Spoiler */}
      <rect x="200" y="75" width="100" height="3" 
            fill="rgba(255,255,255,0.15)" 
            stroke="rgba(255,255,255,0.2)" 
            strokeWidth="0.5"
            rx="1"
            className="car-spoiler"/>
      
      {/* Side Skirts */}
      <rect x="60" y="155" width="380" height="2" 
            fill="rgba(255,255,255,0.1)" 
            stroke="rgba(255,255,255,0.15)" 
            strokeWidth="0.5"
            className="car-side-skirt"/>
      
      {/* Front Bumper */}
      <path d="M 60 160 Q 50 160 45 165 Q 40 170 40 175 Q 40 180 45 185 Q 50 190 60 190 L 440 190 Q 450 190 455 185 Q 460 180 460 175 Q 460 170 455 165 Q 450 160 440 160 Z" 
            fill="rgba(255,255,255,0.1)" 
            stroke="rgba(255,255,255,0.08)" 
            strokeWidth="1"
            className="car-bumper"/>
    </svg>
  </div>
);

export default CarBackground; 