import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import StepBar from '../../components/StepBar';
import { useStepContext } from '../../contexts/StepContext';

import styles from '../orderForm/OrderFormPage.module.css';
import Checkbox from '../../components/Checkbox';

interface RecommendContractFormProps {
  onBack: () => void;
  onNext: () => void;
  activeSpecial?: string | null;
  lowerLimitLiability?: boolean;
  setLowerLimitLiability?: (value: boolean) => void;
}

// Hook for mobile detection
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth <= 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile;
};

const RecommendContractForm: React.FC<RecommendContractFormProps> = ({
  onBack,
  onNext,
  lowerLimitLiability = false,
  setLowerLimitLiability
}) => {
  const isMobile = useIsMobile();
  const [retail, setRetail] = useState('');
  const [showMarkup, setShowMarkup] = useState(false);
  const [markupAmount, setMarkupAmount] = useState('');
  const [suggestedPlan, setSuggestedPlan] = useState('');
  const [suggestedTerm, setSuggestedTerm] = useState('');
  const [suggestedDeductible, setSuggestedDeductible] = useState('');

  const { currentStepId, goToStep, markStepCompleted, getCurrentSteps } = useStepContext();

  // Auto-fill suggested plan when retail is entered
  useEffect(() => {
    if (retail && !suggestedPlan) {
      setSuggestedPlan('Beyond Essential');
    }
    if (retail && showMarkup && !markupAmount) {
      setMarkupAmount('$40.00');
    }
    if (retail && !suggestedTerm) {
      setSuggestedTerm('12');
    }
    if (retail && !suggestedDeductible) {
      setSuggestedDeductible('$200');
    }
  }, [retail, suggestedPlan, showMarkup, markupAmount, suggestedTerm, suggestedDeductible]);

  const formatCurrency = (value: string) => {
    const numericValue = value.replace(/[^\d.]/g, '');
    if (!numericValue) return '';
    const number = parseFloat(numericValue);
    if (isNaN(number)) return '';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(number);
  };

  const handleRetailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setRetail(value);
  };

  const handleRetailBlur = () => {
    if (retail) {
      const formatted = formatCurrency(retail);
      setRetail(formatted);
    }
  };

  const handleMarkupChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMarkupAmount(value);
  };

  const handleMarkupBlur = () => {
    if (markupAmount) {
      const formatted = formatCurrency(markupAmount);
      setMarkupAmount(formatted);
    }
  };

  const handleRetailFocus = () => {
    // Remove formatting when focused for easier editing
    const numericValue = retail.replace(/[^\d.]/g, '');
    setRetail(numericValue);
  };

  const handleNext = () => {
    markStepCompleted('recommend');
    if (lowerLimitLiability) {
      // Go to adjust-limit if Low Limit is checked
      goToStep('adjust-limit');
    } else {
      // Go directly to summary
      onNext();
    }
  };

  const handleStepNavigation = (stepId: string) => {
    if (stepId === 'order') {
      onBack();
    } else {
      goToStep(stepId);
    }
  };

  const inputStyle = {
    width: '100%',
    padding: isMobile ? '0.6rem' : '0.75rem',
    border: '2px solid #e2e8f0',
    borderRadius: '8px',
    fontSize: isMobile ? '14px' : '15px',
    fontFamily: `'Inter', 'Segoe UI', 'system-ui', 'Arial', sans-serif`,
    outline: 'none',
    transition: 'border-color 0.2s ease',
    backgroundColor: '#fff'
  };

  const selectStyle = {
    ...inputStyle,
    appearance: 'none' as const,
    backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
    backgroundPosition: 'right 0.5rem center',
    backgroundRepeat: 'no-repeat',
    backgroundSize: '1.5em 1.5em',
    paddingRight: '2.5rem'
  };

  return (
    <div className={styles.formContainer} style={{
      maxWidth: 980,
      margin: '0 auto',
      padding: isMobile ? '1rem' : '2rem',
      fontFamily: `'Inter', 'Segoe UI', 'system-ui', 'Arial', sans-serif`
    }}>
      <button
        className="shared-button secondary back-button"
        onClick={onBack}
        style={{ marginBottom: 18, fontSize: 15, padding: '0.5rem 1.3rem' }}
      >
        ← Back
      </button>

      <StepBar
        steps={getCurrentSteps()}
        currentStep={currentStepId}
        onStepClick={handleStepNavigation}
        allowNavigation={true}
      />

      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <h1 style={{
          fontSize: '2rem',
          fontWeight: 700,
          color: '#334155',
          margin: 0,
          letterSpacing: '-0.02em'
        }}>
          Recommend Contract
        </h1>
        <div style={{
          height: 2,
          background: 'linear-gradient(90deg, #2563eb, #dc2626)',
          width: 60,
          margin: '0.5rem auto 0 auto',
          borderRadius: 1
        }} />
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        style={{
          background: '#fff',
          borderRadius: '12px',
          padding: isMobile ? '1.5rem' : '2rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          border: '1px solid #e2e8f0'
        }}
      >
        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : '1fr 1fr',
          gap: isMobile ? '1.5rem' : '2rem',
          marginBottom: '2rem'
        }}>
          {/* Left Column */}
          <div>
            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: 600,
                color: '#374151',
                fontSize: '15px'
              }}>
                Retail Price *
              </label>
              <input
                type="text"
                value={retail}
                onChange={handleRetailChange}
                onBlur={handleRetailBlur}
                onFocus={handleRetailFocus}
                placeholder="Enter retail price"
                style={inputStyle}
                required
              />
            </div>

            <div style={{ marginBottom: '1.5rem' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                marginBottom: '0.5rem'
              }}>
                <Checkbox
                  checked={showMarkup}
                  onChange={(e) => setShowMarkup(e.target.checked)}
                  label="Show Markup"
                />
              </div>
              {showMarkup && (
                <input
                  type="text"
                  value={markupAmount}
                  onChange={handleMarkupChange}
                  onBlur={handleMarkupBlur}
                  placeholder="Enter markup amount"
                  style={inputStyle}
                />
              )}
            </div>
          </div>

          {/* Right Column */}
          <div>
            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: 600,
                color: '#374151',
                fontSize: '15px'
              }}>
                Suggested Plan
              </label>
              <select
                value={suggestedPlan}
                onChange={(e) => setSuggestedPlan(e.target.value)}
                style={selectStyle}
              >
                <option value="">Select a plan</option>
                <option value="Essential">Essential</option>
                <option value="Beyond Essential">Beyond Essential</option>
                <option value="Industry Best">Industry Best</option>
              </select>
            </div>

            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: 600,
                color: '#374151',
                fontSize: '15px'
              }}>
                Suggested Term (months)
              </label>
              <select
                value={suggestedTerm}
                onChange={(e) => setSuggestedTerm(e.target.value)}
                style={selectStyle}
              >
                <option value="">Select term</option>
                <option value="12">12 months</option>
                <option value="24">24 months</option>
                <option value="36">36 months</option>
                <option value="48">48 months</option>
              </select>
            </div>

            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: 600,
                color: '#374151',
                fontSize: '15px'
              }}>
                Suggested Deductible
              </label>
              <select
                value={suggestedDeductible}
                onChange={(e) => setSuggestedDeductible(e.target.value)}
                style={selectStyle}
              >
                <option value="">Select deductible</option>
                <option value="$0">$0</option>
                <option value="$100">$100</option>
                <option value="$200">$200</option>
                <option value="$500">$500</option>
              </select>
            </div>
          </div>
        </div>

        <div style={{ marginBottom: '1.5rem' }}>
          <button
            type="button"
            aria-pressed={lowerLimitLiability}
            className={`shared-button secondary back-button${lowerLimitLiability ? ' lower-limit-active' : ''}`}
            onClick={() => {
              console.log('Checkbox clicked, current value:', lowerLimitLiability);
              setLowerLimitLiability && setLowerLimitLiability(!lowerLimitLiability);
            }}
            style={{
              fontSize: isMobile ? 14 : 15,
              padding: isMobile ? '0.4rem 1rem' : '0.5rem 1.3rem',
              marginBottom: 0,
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            <span style={{
              display: 'inline-block',
              width: 16,
              height: 16,
              border: '2px solid currentColor',
              borderRadius: 3,
              backgroundColor: lowerLimitLiability ? 'currentColor' : 'transparent',
              position: 'relative'
            }}>
              {lowerLimitLiability && (
                <svg
                  viewBox="0 0 16 16"
                  fill="none"
                  stroke="white"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: 10,
                    height: 10
                  }}
                >
                  <polyline points="3,8 6,11 13,4" />
                </svg>
              )}
            </span>
            Low Limit of Liability
          </button>
        </div>

        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: '1rem',
          marginTop: '2rem'
        }}>
          <button
            type="button"
            onClick={onBack}
            className="shared-button secondary"
            style={{
              fontSize: isMobile ? 14 : 15,
              padding: isMobile ? '0.6rem 1.2rem' : '0.75rem 1.5rem'
            }}
          >
            ← Back
          </button>

          <button
            type="button"
            onClick={handleNext}
            className="shared-button primary"
            style={{
              fontSize: isMobile ? 14 : 15,
              padding: isMobile ? '0.6rem 1.2rem' : '0.75rem 1.5rem'
            }}
          >
            Continue →
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default RecommendContractForm;
