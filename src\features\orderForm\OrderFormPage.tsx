import React, { useState, useEffect, useRef } from "react";
import styles from "./OrderFormPage.module.css";
import "../../styles/inputs.css";
import Icon from "../../components/Icon";
import { motion, AnimatePresence } from "framer-motion";
import OrderSummaryForm from '../orderSummary/OrderSummaryForm';
import StepBar from '../../components/StepBar';
import { useStepContext } from '../../contexts/StepContext';
import AdjustLimitForm from '../adjustLimit/AdjustLimitForm';
import { validateNumericInput, formatCurrencyInput, removeCurrencyFormatting } from '../../utils/validation';


// Types
interface OrderFormPageProps {
  input_36: string;   // VIN (was: vin)
  input_4: string;    // Year (was: year)
  input_5: string;    // Make (was: make)
  input_6: string;    // Model (was: model)
  input_7: string;    // Trim (was: trim)
  input_138: string;  // Odometer (was: odometer)
  // Hidden fields for Gravity Forms compatibility
  input_11?: string;  // Body Style
  input_8?: string;   // Engine
  input_9?: string;   // Cylinders
  input_10?: string;  // Drive Type
  input_37?: string;  // Vehicle Class
  onBack: () => void;
  vinDecodeSuccess?: boolean;
}

// Constants
const fontFamily = `'Inter', 'Segoe UI', 'system-ui', 'Arial', sans-serif`;

const CONTRACT_TYPES = [
  {
    key: 'essential',
    label: 'Essential',
    retail: [1000, 1800, 2500, 3200, 3900, 4500, 5000, 5700, 6500, 7500],
  },
  {
    key: 'beyond',
    label: 'Beyond Essential',
    retail: [1200, 2000, 2700, 3400, 4100, 4700, 5200, 5900, 6700, 7700],
  },
  {
    key: 'industry',
    label: 'Industry Best',
    retail: [1400, 2200, 2900, 3600, 4300, 4900, 5400, 6100, 6900, 7900],
  },
];

const TERM_MONTHS = [12, 24, 36, 48, 60, 72, 84, 96, 108, 120];

const tabOptions = [
  { value: 'option1', label: "Generating a Buyer's Order" },
  { value: 'option2', label: 'Entering Loan Amount Only' },
  { value: 'option3', label: 'VSC Details Only' },
  { value: 'option4', label: 'Recommend Contract' },
  { value: 'option5', label: 'Price Match' },
];

const COVERAGE_INFO = {
  essential: "Essential: Covers major powertrain components and basic systems.",
  beyond: "Beyond Essential: Includes Essential plus more electrical and comfort features.",
  industry: "Industry Best: Our most comprehensive plan, covering nearly all mechanical and electrical components.",
};

// Custom Hooks
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 800);
  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth <= 800);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  return isMobile;
};

const useScrollPosition = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Determine scroll direction
      const scrollingUp = currentScrollY < lastScrollY;

      // Show sticky bar if scrolled down and not scrolling up
      // Hide if at top or scrolling up
      setIsScrolled(currentScrollY > 50 && !scrollingUp);

      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  return isScrolled;
};

// Utility functions
const getInputStyle = (isMobile: boolean) => ({
  fontSize: isMobile ? 14 : 15,
  padding: isMobile ? '0.35rem 0.5rem' : '0.38rem 0.6rem',
  background: '#fff',
  border: '1px solid #e5e7eb',
  borderRadius: 5,
  height: isMobile ? 34 : 36
});

const getLabelStyle = (isMobile: boolean) => ({
  fontSize: isMobile ? 12 : 13,
  fontWeight: 500,
  color: '#64748b',
  marginBottom: 4,
  display: 'block'
});

// Main Component
const OrderFormPage: React.FC<OrderFormPageProps> = ({
  input_36: vin, input_4: year, input_5: make, input_6: model, input_7: trim, input_138: odometer,
  input_11: bodyStyle, input_8: engine, input_9: cylinders, input_10: driveType, input_37: vehicleClass,
  onBack, vinDecodeSuccess
}) => {
  // Custom hooks
  const isMobile = useIsMobile();
  const isScrolled = useScrollPosition();
  const {
    currentStepId,
    goToStep,
    markStepCompleted,
    initializeFlow,
    updateStepsOnly,
    getNextStepInFlow,
    getPreviousStep,
    getCurrentSteps
  } = useStepContext();

  // State declarations
  const [selectedOption, setSelectedOption] = useState('option1');
  const [buyerOrder, setBuyerOrder] = useState<{ [key: string]: string }>({});
  const [vscSelection, setVscSelection] = useState<{ contract: string; term: number | null }>({ contract: 'essential', term: null });
  const [surcharges, setSurcharges] = useState<string[]>([]);
  const [deductible, setDeductible] = useState<string>('100');
  const [vscPrice, setVscPrice] = useState("");
  const [loanTerm, setLoanTerm] = useState("");
  const [interestRate, setInterestRate] = useState("");
  // Additional payment calculator fields for tabs 1 & 2
  const [amountFinanced, setAmountFinanced] = useState("");
  const [downPayment, setDownPayment] = useState("");
  // const [totalLoanAfterRev, setTotalLoanAfterRev] = useState("");
  const [activeSpecial, setActiveSpecial] = useState<string | null>(null);
  const [recommendLowLimit, setRecommendLowLimit] = useState(false);
  const [recommendData, setRecommendData] = useState({
    retailPrice: '',
    showMarkup: false,
    markupAmount: '',
    suggestedPlan: '',
    suggestedTerm: '',
    suggestedDeductible: ''
  });



  // Auto-fill suggested fields when retail price is entered
  useEffect(() => {
    if (recommendData.retailPrice && !recommendData.suggestedPlan) {
      setRecommendData(prev => ({
        ...prev,
        suggestedPlan: 'Beyond Essential',
        suggestedTerm: '12',
        suggestedDeductible: '$200'
      }));
    }
    if (recommendData.retailPrice && recommendData.showMarkup && !recommendData.markupAmount) {
      setRecommendData(prev => ({ ...prev, markupAmount: '$40.00' }));
    }
  }, [recommendData.retailPrice, recommendData.showMarkup, recommendData.suggestedPlan, recommendData.markupAmount]);



  const handleRetailPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = validateNumericInput(e.target.value, true);
    setRecommendData(prev => ({ ...prev, retailPrice: value }));
  };

  const handleRetailPriceBlur = () => {
    if (recommendData.retailPrice) {
      const formatted = formatCurrencyInput(recommendData.retailPrice);
      setRecommendData(prev => ({ ...prev, retailPrice: formatted }));
    }
  };

  const handleRetailPriceFocus = () => {
    // Remove formatting when focused for easier editing
    const numericValue = removeCurrencyFormatting(recommendData.retailPrice);
    setRecommendData(prev => ({ ...prev, retailPrice: numericValue }));
  };

  const handleMarkupAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = validateNumericInput(e.target.value, true);
    setRecommendData(prev => ({ ...prev, markupAmount: value }));
  };

  const handleMarkupAmountBlur = () => {
    if (recommendData.markupAmount) {
      const formatted = formatCurrencyInput(recommendData.markupAmount);
      setRecommendData(prev => ({ ...prev, markupAmount: formatted }));
    }
  };

  const handleMarkupAmountFocus = () => {
    // Remove formatting when focused for easier editing
    const numericValue = removeCurrencyFormatting(recommendData.markupAmount);
    setRecommendData(prev => ({ ...prev, markupAmount: numericValue }));
  };
  const [showPriceMatchForm, setShowPriceMatchForm] = useState(false);
  const [tooltip, setTooltip] = useState<{ type: string | null, anchor: HTMLElement | null }>({ type: null, anchor: null });
  const [showDealerInfoForm, setShowDealerInfoForm] = useState(false);
  const [lowerLimitFocused, setLowerLimitFocused] = useState(false);
  const [priceMatchData, setPriceMatchData] = useState({
    competitorCoverage: '',
    competitorTerm: '',
    providerName: '',
    productName: '',
    dealerCost: '',
    markup: '',
    totalPrice: '0'
  });

  // Refs
  const tabRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const tabBarRef = useRef<HTMLDivElement | null>(null);

  // Computed values
  const inputStyle = getInputStyle(isMobile);
  const labelStyle = getLabelStyle(isMobile);

  // Enhanced input style for recommend form
  const recommendInputStyle = {
    width: '100%',
    padding: isMobile ? '0.75rem' : '0.875rem',
    border: '2px solid #e2e8f0',
    borderRadius: '8px',
    fontSize: isMobile ? '14px' : '15px',
    fontFamily: `'Inter', 'Segoe UI', 'system-ui', 'Arial', sans-serif`,
    outline: 'none',
    transition: 'all 0.2s ease',
    backgroundColor: '#fff',
    color: '#374151',
    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
    '&:focus': {
      borderColor: '#2563eb',
      boxShadow: '0 0 0 3px rgba(37, 99, 235, 0.1)'
    },
    '&:hover': {
      borderColor: '#cbd5e1'
    }
  };

  const recommendLabelStyle = {
    display: 'block',
    marginBottom: '0.5rem',
    fontWeight: 600,
    color: '#374151',
    fontSize: isMobile ? '14px' : '15px',
    fontFamily: `'Inter', 'Segoe UI', 'system-ui', 'Arial', sans-serif`
  };

  const recommendSelectStyle = {
    ...recommendInputStyle,
    appearance: 'none' as const,
    backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
    backgroundPosition: 'right 0.75rem center',
    backgroundRepeat: 'no-repeat',
    backgroundSize: '1.25em 1.25em',
    paddingRight: '2.5rem'
  };

  // Effects
  useEffect(() => {
    // Initialize flow based on selected option and low limit state
    let hasLowLimit = false;

    if (selectedOption === 'option4') {
      // For Recommend Contract, use separate state
      hasLowLimit = recommendLowLimit;
    } else {
      // For other options, use activeSpecial
      hasLowLimit = activeSpecial === 'lowerLimit';
    }

    initializeFlow(selectedOption, hasLowLimit, 'order');
  }, [initializeFlow, selectedOption, activeSpecial, recommendLowLimit]);

  useEffect(() => {
    const handleClickOutside = () => setTooltip({ type: null, anchor: null });
    if (tooltip.type) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [tooltip.type]);

  // Event handlers
  const handleTooltip = (type: string, event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    setTooltip(prev =>
      prev.type === type ? { type: null, anchor: null } : { type, anchor: event.currentTarget }
    );
  };

  const handleBuyerOrderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setBuyerOrder({ ...buyerOrder, [e.target.name]: e.target.value });
  };

  // Specialized handler for currency fields
  const handleCurrencyFieldChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = validateNumericInput(e.target.value, true);
    setBuyerOrder({ ...buyerOrder, [e.target.name]: value });
  };

  const handleCurrencyFieldBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    if (e.target.value) {
      const formatted = formatCurrencyInput(e.target.value);
      setBuyerOrder({ ...buyerOrder, [e.target.name]: formatted });
    }
  };

  const handleCurrencyFieldFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    const numericValue = removeCurrencyFormatting(e.target.value);
    setBuyerOrder({ ...buyerOrder, [e.target.name]: numericValue });
  };

  // VSC Price currency handlers
  const handleVscPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = validateNumericInput(e.target.value, true);
    setVscPrice(value);
  };

  const handleVscPriceBlur = () => {
    if (vscPrice) {
      const formatted = formatCurrencyInput(vscPrice);
      setVscPrice(formatted);
    }
  };

  const handleVscPriceFocus = () => {
    const numericValue = removeCurrencyFormatting(vscPrice);
    setVscPrice(numericValue);
  };



  // Down Payment currency handlers
  const handleDownPaymentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = validateNumericInput(e.target.value, true);
    setDownPayment(value);
  };

  const handleDownPaymentBlur = () => {
    if (downPayment) {
      const formatted = formatCurrencyInput(downPayment);
      setDownPayment(formatted);
    }
  };

  const handleDownPaymentFocus = () => {
    const numericValue = removeCurrencyFormatting(downPayment);
    setDownPayment(numericValue);
  };

  const handleSurchargeChange = (key: string, checked: boolean) => {
    setSurcharges(prev => checked ? [...prev, key] : prev.filter(k => k !== key));
  };

  const closeTooltip = () => setTooltip({ type: null, anchor: null });

  const handleLowerLimitToggle = () => {
    setActiveSpecial(prev => prev === 'lowerLimit' ? null : 'lowerLimit');
  }; // Removed the onFocus and onBlur handlers


  // Calculate Amount Financed from buyer order data
  const calculatedAmountFinanced = (() => {
    const cashPrice = parseFloat(buyerOrder['input_71']?.replace(/[^\d.]/g, '') || '0') || 0;
    const accessories = parseFloat(buyerOrder['input_123']?.replace(/[^\d.]/g, '') || '0') || 0;
    const docFee = parseFloat(buyerOrder['input_124']?.replace(/[^\d.]/g, '') || '0') || 0;
    const otherExpense = parseFloat(buyerOrder['input_173']?.replace(/[^\d.]/g, '') || '0') || 0;
    const tradeInValue = parseFloat(buyerOrder['input_117']?.replace(/[^\d.]/g, '') || '0') || 0;
    const balanceOwedTradeIn = parseFloat(buyerOrder['input_126']?.replace(/[^\d.]/g, '') || '0') || 0;
    const cashDeposit = parseFloat(buyerOrder['input_128']?.replace(/[^\d.]/g, '') || '0') || 0;
    const rebate = parseFloat(buyerOrder['input_129']?.replace(/[^\d.]/g, '') || '0') || 0;
    const otherCredit = parseFloat(buyerOrder['input_174']?.replace(/[^\d.]/g, '') || '0') || 0;

    const total = cashPrice + accessories + docFee + otherExpense - tradeInValue + balanceOwedTradeIn - cashDeposit - rebate - otherCredit;
    return total > 0 ? `$${total.toFixed(2)}` : '$120.00';
  })();

  // Update amountFinanced when buyer order changes
  React.useEffect(() => {
    setAmountFinanced(calculatedAmountFinanced);
  }, [buyerOrder, calculatedAmountFinanced]);

  // Calculations
  // const totalLoanAfterRevCalculated = (() => {
  //   const financed = parseFloat(amountFinanced.replace(/[^\d.]/g, '')) || 0;
  //   const vsc = parseFloat(vscPrice.replace(/[^\d.]/g, '')) || 0;
  //   const down = parseFloat(downPayment.replace(/[^\d.]/g, '')) || 0;
  //   const total = financed + vsc - down;
  //   return total > 0 ? `$${total.toFixed(2)}` : '$0.00';
  // })();

  // Update totalLoanAfterRev when dependencies change
  // React.useEffect(() => {
  //   setTotalLoanAfterRev(totalLoanAfterRevCalculated);
  // }, [amountFinanced, vscPrice, downPayment, totalLoanAfterRevCalculated]);

  const estimatedPayment = (() => {
    // For tabs 1 & 2, use total loan amount; for tab 3, use VSC price only
    const P = (selectedOption === 'option1' || selectedOption === 'option2')
      ? 0
      : parseFloat(vscPrice.replace(/[^\d.]/g, '')) || 0;
    const n = parseInt(loanTerm) || 0;
    const r = (parseFloat(interestRate) || 0) / 100 / 12;
    if (!P || !n) return "0.00";
    if (!r) return (P / n).toFixed(2);
    const payment = (P * r) / (1 - Math.pow(1 + r, -n));
    return payment ? payment.toFixed(2) : "0.00";
  })();

  const estimatedPaymentWithoutVSC = (() => {
    if (selectedOption !== 'option1' && selectedOption !== 'option2') return "0.00";
    const financed = parseFloat(amountFinanced.replace(/[^\d.]/g, '')) || 0;
    const down = parseFloat(downPayment.replace(/[^\d.]/g, '')) || 0;
    const P = financed - down;
    const n = parseInt(loanTerm) || 0;
    const r = (parseFloat(interestRate) || 0) / 100 / 12;
    if (!P || !n || P <= 0) return "0.00";
    if (!r) return (P / n).toFixed(2);
    const payment = (P * r) / (1 - Math.pow(1 + r, -n));
    return payment ? payment.toFixed(2) : "0.00";
  })();

  const paymentDifference = (() => {
    const withVSC = parseFloat(estimatedPayment) || 0;
    const withoutVSC = parseFloat(estimatedPaymentWithoutVSC) || 0;
    const diff = withVSC - withoutVSC;
    return diff > 0 ? diff.toFixed(2) : "0.00";
  })();

  const handleProceedToNext = () => {
    markStepCompleted('order');

    // Get the next step based on current flow
    const nextStep = getNextStepInFlow('order');

    if (nextStep) {
      goToStep(nextStep);

      // Handle special cases for different steps
      if (nextStep === 'summary') {
        setShowDealerInfoForm(true);
      }
    }
  };

  // Custom step navigation that maintains completion status
  const handleStepNavigation = (stepId: string) => {
    goToStep(stepId);
    if (stepId === 'order') {
      // Stay on current form
    } else if (stepId === 'adjust-limit') {
      // This will be handled by the conditional rendering
    } else if (stepId === 'summary') {
      setShowDealerInfoForm(true);
    }
  };

  const tabData = (
    <>
      <div className={styles.dividerRow}>
        <div className={styles.dividerLine} />
        <div className={styles.dividerTitle}>Choose Your Coverage</div>
        <div className={styles.dividerLine} />
      </div>
      {/* Desktop Table View */}
      <div className={`${styles.tableScrollWrapper} ${styles.desktopOnly}`}>
        <table className={styles.fixedTable} style={{ touchAction: 'pan-x' }}>
          <thead>
            <tr>
              <th className={styles.coverageTableHeadCellTermAuto}>Term</th>
              <th className={`${styles.coverageTableHeadCell} ${styles.coverageEssential}`}
                style={{ position: 'relative', minWidth: 120 }}>
                Essential
                <button
                  type="button"
                  aria-label="Show info about Essential"
                  style={{ background: 'none', border: 'none', boxShadow: 'none', outline: 'none', cursor: 'pointer', marginLeft: 5, paddingBottom: 2.4, verticalAlign: 'middle', padding: 0, lineHeight: 0 }}
                  onClick={e => handleTooltip('essential', e)}
                  tabIndex={0}
                >
                  <svg width="13" height="13" viewBox="0 0 13 13" style={{ display: 'inline', verticalAlign: 'middle' }}>
                    <circle cx="6.5" cy="6.5" r="6.5" fill="#64748b" />
                    <text x="6.5" y="6.5" textAnchor="middle" dominantBaseline="middle" fontSize="9" fontWeight="bold" fill="#fff" fontFamily="Arial, sans-serif">i</text>
                  </svg>
                </button>
                {tooltip.type === 'essential' && (
                  <div style={{ position: 'absolute', left: '50%', bottom: '110%', transform: 'translateX(-50%)', background: '#fff', color: '#334155', border: '1.5px solid #e5e7eb', borderRadius: 8, boxShadow: '0 4px 24px rgba(30,41,59,0.13)', padding: '0.9rem 1.2rem', fontSize: 15, zIndex: 100, minWidth: 200, whiteSpace: 'normal' }}>
                    <div style={{ position: 'absolute', bottom: -8, left: '50%', transform: 'translateX(-50%)', width: 0, height: 0, borderLeft: '8px solid transparent', borderRight: '8px solid transparent', borderTop: '8px solid #e5e7eb' }} />
                    <div style={{ position: 'absolute', bottom: -7, left: '50%', transform: 'translateX(-50%)', width: 0, height: 0, borderLeft: '7px solid transparent', borderRight: '7px solid transparent', borderTop: '7px solid #fff' }} />
                    {COVERAGE_INFO.essential}
                    <button onClick={closeTooltip} style={{ background: 'none', border: 'none', color: '#64748b', fontSize: 18, float: 'right', cursor: 'pointer', marginLeft: 8, marginTop: -2 }}>×</button>
                  </div>
                )}
              </th>
              <th className={`${styles.coverageTableHeadCell} ${styles.coverageBeyond}`}
                style={{ position: 'relative', minWidth: 140 }}>
                Beyond Essential
                <button
                  type="button"
                  aria-label="Show info about Beyond Essential"
                  style={{ background: 'none', border: 'none', boxShadow: 'none', outline: 'none', cursor: 'pointer', marginLeft: 5, paddingBottom: 2.4, verticalAlign: 'middle', padding: 0, lineHeight: 0 }}
                  onClick={e => handleTooltip('beyond', e)}
                  tabIndex={0}
                >
                  <svg width="13" height="13" viewBox="0 0 13 13" style={{ display: 'inline', verticalAlign: 'middle' }}>
                    <circle cx="6.5" cy="6.5" r="6.5" fill="#334155" />
                    <text x="6.5" y="6.5" textAnchor="middle" dominantBaseline="middle" fontSize="9" fontWeight="bold" fill="#fff" fontFamily="Arial, sans-serif">i</text>
                  </svg>
                </button>
                {tooltip.type === 'beyond' && (
                  <div style={{ position: 'absolute', left: '50%', bottom: '110%', transform: 'translateX(-50%)', background: '#fff', color: '#334155', border: '1.5px solid #e5e7eb', borderRadius: 8, boxShadow: '0 4px 24px rgba(30,41,59,0.13)', padding: '0.9rem 1.2rem', fontSize: 15, zIndex: 100, minWidth: 220, whiteSpace: 'normal' }}>
                    <div style={{ position: 'absolute', bottom: -8, left: '50%', transform: 'translateX(-50%)', width: 0, height: 0, borderLeft: '8px solid transparent', borderRight: '8px solid transparent', borderTop: '8px solid #e5e7eb' }} />
                    <div style={{ position: 'absolute', bottom: -7, left: '50%', transform: 'translateX(-50%)', width: 0, height: 0, borderLeft: '7px solid transparent', borderRight: '7px solid transparent', borderTop: '7px solid #fff' }} />
                    {COVERAGE_INFO.beyond}
                    <button onClick={closeTooltip} style={{ background: 'none', border: 'none', color: '#334155', fontSize: 18, float: 'right', cursor: 'pointer', marginLeft: 8, marginTop: -2 }}>×</button>
                  </div>
                )}
              </th>
              <th className={`${styles.coverageTableHeadCell} ${styles.coverageBest}`}
                style={{ position: 'relative', display: 'flex', alignItems: 'center', gap: 5, justifyContent: 'center', width: '100%', minWidth: 200, whiteSpace: 'nowrap' }}>
                <span style={{ display: 'flex', alignItems: 'center', gap: 5, whiteSpace: 'nowrap' }}>
                  Industry Best <span className={styles.recommendedBadge} style={{ fontSize: '10px', marginLeft: '4px', marginRight: '4px' }}>Recommended</span>
                  <button
                    type="button"
                    aria-label="Show info about Industry Best"
                    style={{ background: 'none', border: 'none', boxShadow: 'none', outline: 'none', cursor: 'pointer', padding: 0, margin: 0, verticalAlign: 'middle', lineHeight: 0, display: 'flex', alignItems: 'center' }}
                    onClick={e => handleTooltip('industry', e)}
                    tabIndex={0}
                  >
                    <svg width="13" height="13" viewBox="0 0 13 13" style={{ display: 'inline', verticalAlign: 'middle' }}>
                      <circle cx="6.5" cy="6.5" r="6.5" fill="#b91c1c" />
                      <text x="6.5" y="6.5" textAnchor="middle" dominantBaseline="middle" fontSize="9" fontWeight="bold" fill="#fff" fontFamily="Arial, sans-serif">i</text>
                    </svg>
                  </button>
                </span>
                {tooltip.type === 'industry' && (
                  <div style={{ position: 'absolute', left: '50%', bottom: '110%', transform: 'translateX(-50%)', background: '#fff', color: '#b91c1c', border: '1.5px solid #fecaca', borderRadius: 8, boxShadow: '0 4px 24px rgba(30,41,59,0.13)', padding: '0.9rem 1.2rem', fontSize: 15, zIndex: 100, minWidth: 240, whiteSpace: 'normal' }}>
                    <div style={{ position: 'absolute', bottom: -8, left: '50%', transform: 'translateX(-50%)', width: 0, height: 0, borderLeft: '8px solid transparent', borderRight: '8px solid transparent', borderTop: '8px solid #fecaca' }} />
                    <div style={{ position: 'absolute', bottom: -7, left: '50%', transform: 'translateX(-50%)', width: 0, height: 0, borderLeft: '7px solid transparent', borderRight: '7px solid transparent', borderTop: '7px solid #fff' }} />
                    {COVERAGE_INFO.industry}
                    <button onClick={closeTooltip} style={{ background: 'none', border: 'none', color: '#b91c1c', fontSize: 18, float: 'right', cursor: 'pointer', marginLeft: 8, marginTop: -2 }}>×</button>
                  </div>
                )}
              </th>
            </tr>
          </thead>
          <tbody>
            {TERM_MONTHS.map((months, idx) => (
              <tr key={months} style={{ borderBottom: '1px solid #f1f5f9', transition: 'background 0.18s' }}>
                <td className={styles.coverageTableHeadCellTermAuto} style={{ padding: '5px 8px', fontWeight: 600, color: '#334155', fontSize: 13 }}>{months}mo</td>
                <td className={`${styles.coverageTableHeadCell} ${styles.coverageEssential} ${vscSelection.contract === CONTRACT_TYPES[0].key && vscSelection.term === months ? styles.coverageSelected : ''}`} style={{ textAlign: 'center', borderRadius: 6, position: 'relative', cursor: 'pointer' }}
                  onClick={() => setVscSelection({ contract: CONTRACT_TYPES[0].key, term: months })}
                  tabIndex={0}
                  aria-label={`Select Essential ${months}mo`}
                >
                  <span style={{ display: 'inline-block', fontWeight: vscSelection.contract === CONTRACT_TYPES[0].key && vscSelection.term === months ? 700 : 500 }}>
                    ${CONTRACT_TYPES[0].retail[idx].toLocaleString()}
                  </span>
                </td>
                <td className={`${styles.coverageTableHeadCell} ${styles.coverageBeyond} ${vscSelection.contract === CONTRACT_TYPES[1].key && vscSelection.term === months ? styles.coverageSelected : ''}`} style={{ textAlign: 'center', borderRadius: 6, position: 'relative', cursor: 'pointer' }}
                  onClick={() => setVscSelection({ contract: CONTRACT_TYPES[1].key, term: months })}
                  tabIndex={0}
                  aria-label={`Select Beyond Essential ${months}mo`}
                >
                  <span style={{ display: 'inline-block', fontWeight: vscSelection.contract === CONTRACT_TYPES[1].key && vscSelection.term === months ? 700 : 500 }}>
                    ${CONTRACT_TYPES[1].retail[idx].toLocaleString()}
                  </span>
                </td>
                <td className={`${styles.coverageTableHeadCell} ${styles.coverageBest} ${vscSelection.contract === CONTRACT_TYPES[2].key && vscSelection.term === months ? styles.coverageSelected : ''} ${styles.sparkleEffect}`} style={{ textAlign: 'center', borderRadius: 6, position: 'relative', cursor: 'pointer' }}
                  onClick={() => setVscSelection({ contract: CONTRACT_TYPES[2].key, term: months })}
                  tabIndex={0}
                  aria-label={`Select Industry Best ${months}mo`}
                >
                  <span style={{ display: 'inline-block', fontWeight: vscSelection.contract === CONTRACT_TYPES[2].key && vscSelection.term === months ? 700 : 500 }}>
                    ${CONTRACT_TYPES[2].retail[idx].toLocaleString()}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className={styles.mobileOnly}>
        {/* Coverage Type Selector */}
        <div className={styles.mobileCoverageSelector}>
          {CONTRACT_TYPES.map((contractType) => {
            const isSelected = vscSelection.contract === contractType.key;
            const isRecommended = contractType.key === 'industry';

            return (
              <button
                key={contractType.key}
                className={`${styles.mobileCoverageTab} ${
                  contractType.key === 'essential' ? styles.coverageEssential :
                  contractType.key === 'beyond' ? styles.coverageBeyond :
                  styles.coverageBest
                } ${isSelected ? styles.coverageSelected : ''} ${isRecommended ? styles.recommendedTab : ''}`}
                onClick={() => setVscSelection(prev => ({ ...prev, contract: contractType.key }))}
              >
                <div className={styles.mobileCoverageTabContent}>
                  <div className={styles.mobileCoverageTabTitle}>
                    {contractType.label}
                  </div>
                  <div className={styles.mobileCoverageTabSubtitle}>
                    {contractType.key === 'essential' && 'Basic Coverage'}
                    {contractType.key === 'beyond' && 'Enhanced Coverage'}
                    {contractType.key === 'industry' && 'Premium Coverage'}
                  </div>
                  {isRecommended && (
                    <div className={styles.mobileCoverageTabBadge}>
                      <span className={styles.recommendedBadgeText}>⭐ Recommended</span>
                    </div>
                  )}
                </div>
              </button>
            );
          })}
        </div>

        {/* Selected Coverage Info with Fade Animation */}
        <AnimatePresence mode="wait">
          <motion.div
            key={vscSelection.contract}
            className={styles.mobileSelectedCoverage}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
          >
            <div className={styles.mobileSelectedTitle}>
              {CONTRACT_TYPES.find(c => c.key === vscSelection.contract)?.label || 'Essential'}
            </div>
            <div className={styles.mobileSelectedDescription}>
              {COVERAGE_INFO[vscSelection.contract as keyof typeof COVERAGE_INFO] || COVERAGE_INFO.essential}
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Term Grid with Fade Animation */}
        <AnimatePresence mode="wait">
          <motion.div
            key={vscSelection.contract}
            className={styles.mobileTermGrid}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.4, ease: "easeInOut" }}
          >
            {TERM_MONTHS.map((months, idx) => {
              const selectedContract = CONTRACT_TYPES.find(c => c.key === vscSelection.contract) || CONTRACT_TYPES[0];
              const price = selectedContract.retail[idx];
              const isSelected = vscSelection.term === months;

              return (
                <motion.button
                  key={months}
                  className={`${styles.mobileTermGridItem} ${
                    vscSelection.contract === 'essential' ? styles.coverageEssential :
                    vscSelection.contract === 'beyond' ? styles.coverageBeyond :
                    styles.coverageBest
                  } ${isSelected ? styles.coverageSelected : ''}`}
                  onClick={() => setVscSelection(prev => ({ ...prev, term: months }))}
                  aria-label={`Select ${months} months for $${price.toLocaleString()}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: idx * 0.05, ease: "easeOut" }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className={styles.mobileTermGridTerm}>{months}mo</div>
                  <div className={styles.mobileTermGridPrice}>${price.toLocaleString()}</div>
                </motion.button>
              );
            })}
          </motion.div>
        </AnimatePresence>

        {/* Quick Selection Buttons with Fade Animation */}
        <AnimatePresence mode="wait">
          <motion.div
            key={`quick-${vscSelection.contract}`}
            className={styles.mobileQuickSelect}
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -15 }}
            transition={{ duration: 0.4, delay: 0.2, ease: "easeInOut" }}
          >
            <div className={styles.mobileQuickSelectTitle}>Popular Choices:</div>
            <div className={styles.mobileQuickSelectButtons}>
              <motion.button
                className={`${styles.mobileQuickSelectBtn} ${styles.coverageBest}`}
                onClick={() => setVscSelection({ contract: 'industry', term: 60 })}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.3 }}
                whileHover={{ scale: 1.02, x: 5 }}
                whileTap={{ scale: 0.98 }}
              >
                Industry Best • 60mo
                <span className={styles.mobileQuickSelectPrice}>
                  ${CONTRACT_TYPES[2].retail[4].toLocaleString()}
                </span>
              </motion.button>
              <motion.button
                className={`${styles.mobileQuickSelectBtn} ${styles.coverageBeyond}`}
                onClick={() => setVscSelection({ contract: 'beyond', term: 48 })}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.4 }}
                whileHover={{ scale: 1.02, x: 5 }}
                whileTap={{ scale: 0.98 }}
              >
                Beyond Essential • 48mo
                <span className={styles.mobileQuickSelectPrice}>
                  ${CONTRACT_TYPES[1].retail[3].toLocaleString()}
                </span>
              </motion.button>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>
      <div className={styles.dividerRow}>
        <div className={styles.dividerLine} />
        <div className={styles.dividerTitle}>Select Surcharges</div>
        <div className={styles.dividerLine} />
      </div>
      <div className={styles.surchargesCard}>
        <div className={styles.surchargesColumn}>
          <div className={styles.surchargesTitle}>Surcharges</div>
          <label className={styles.surchargeOption}>
            <input type="checkbox" className={styles.surchargeCheckbox} id="choice_98_16_1" name="input_16.1" value="250.01" checked={surcharges.includes('evHybrid')} onChange={e => handleSurchargeChange('evHybrid', e.target.checked)} /> EV/Hybrid
          </label>
          <label className={styles.surchargeOption}>
            <input type="checkbox" className={styles.surchargeCheckbox} id="choice_98_16_2" name="input_16.2" value="250.02" checked={surcharges.includes('brandedTitle')} onChange={e => handleSurchargeChange('brandedTitle', e.target.checked)} /> Branded Title
          </label>
          <label className={styles.surchargeOption}>
            <input type="checkbox" className={styles.surchargeCheckbox} id="choice_98_16_3" name="input_16.3" value="250.03" checked={surcharges.includes('commercialUse')} onChange={e => handleSurchargeChange('commercialUse', e.target.checked)} /> Commercial Use
          </label>
        </div>
        <div className={styles.surchargesColumn}>
          <div className={styles.surchargesTitle}>Deductible</div>
          <label className={styles.deductibleOption}>
            <input type="radio" className={styles.deductibleRadio} id="choice_98_15_0" name="input_15" value="100" checked={deductible === '100'} onChange={() => setDeductible('100')} /> $100
          </label>
          <label className={styles.deductibleOption}>
            <input type="radio" className={styles.deductibleRadio} id="choice_98_15_1" name="input_15" value="200" checked={deductible === '200'} onChange={() => setDeductible('200')} /> $200
          </label>
        </div>
      </div>
      <div className={styles.dividerRow}>
        <div className={styles.dividerLine} />
        <div className={styles.dividerTitle}>Payment Calculator</div>
        <div className={styles.dividerLine} />
      </div>
      <div className={styles.paymentCalculatorCard}>
        {/* Enhanced payment calculator for tabs 1 & 2 */}
        {(selectedOption === 'option1' || selectedOption === 'option2') ? (
          <>
            {/* First row - Amount Financed, VSC Price, Down Payment */}
            <div className={styles.paymentInputRow}>
              <div className={styles.inputGroup}>
                <label className={styles.paymentInputLabel}>Amount Financed</label>
                <div className={`${styles.paymentInput} ${styles.paymentShortInput}`} style={{
                  background: '#f8fafc',
                  color: '#64748b',
                  display: 'flex',
                  alignItems: 'center',
                  fontWeight: '500'
                }}>
                  {amountFinanced || '$120.00'}
                </div>
              </div>
              <div className={styles.inputGroup}>
                <label className={styles.paymentInputLabel} htmlFor="input_98_82">VSC Price</label>
                <input
                  id="input_98_82"
                  name="input_82"
                  className={`${styles.paymentInput} ${styles.paymentShortInput}`}
                  type="text"
                  value={vscPrice}
                  onChange={handleVscPriceChange}
                  onBlur={handleVscPriceBlur}
                  onFocus={handleVscPriceFocus}
                  placeholder="$120.00"
                />
              </div>
              <div className={styles.inputGroup}>
                <label className={styles.paymentInputLabel} htmlFor="input_98_276">Down Payment</label>
                <input
                  id="input_98_276"
                  name="input_276"
                  className={`${styles.paymentInput} ${styles.paymentShortInput}`}
                  type="text"
                  value={downPayment}
                  onChange={handleDownPaymentChange}
                  onBlur={handleDownPaymentBlur}
                  onFocus={handleDownPaymentFocus}
                  placeholder="$0.00"
                />
              </div>
            </div>

            {/* Second row - Total loan amount after Rev products */}
            <div className={styles.paymentInputRow}>
              <div className={styles.inputGroup}>
                <label className={styles.paymentInputLabel} style={{
                  fontWeight: '600',
                  color: '#1e293b',
                  fontSize: '14px'
                }}>
                  Total loan amount after Rev products
                </label>
                {/* <div className={`${styles.paymentInput} ${styles.paymentShortInput}`} style={{
                  background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
                  color: '#1e293b',
                  display: 'flex',
                  alignItems: 'center',
                  fontWeight: '700',
                  fontSize: '16px',
                  border: '2px solid #cbd5e1',
                  borderRadius: '8px',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                  minHeight: '44px'
                }}>
                  {totalLoanAfterRev || '$0.00'}
                </div> */}
              </div>
            </div>

            {/* Third row - Loan Term, Interest Rate */}
            <div className={styles.paymentInputRow}>
              <div className={styles.inputGroup}>
                <label className={styles.paymentInputLabel} htmlFor="input_98_258">Loan Term (Months)</label>
                <input
                  id="input_98_258"
                  name="input_258"
                  className={`${styles.paymentInput} ${styles.paymentShortInput}`}
                  type="number"
                  min="1"
                  value={loanTerm}
                  onChange={e => setLoanTerm(e.target.value)}
                  placeholder="72"
                />
              </div>
              <div className={styles.inputGroup}>
                <label className={styles.paymentInputLabel} htmlFor="input_98_34">Interest Rate (%)</label>
                <input
                  id="input_98_34"
                  name="input_34"
                  className={`${styles.paymentInput} ${styles.paymentShortInput}`}
                  type="number"
                  min="0"
                  step="0.01"
                  value={interestRate}
                  onChange={e => setInterestRate(e.target.value)}
                  placeholder="6.74%"
                />
              </div>
            </div>

            {/* Payment results row */}
            <div className={styles.paymentInputRow}>
              <div className={styles.inputGroup}>
                <label className={styles.paymentInputLabel}>Estimated Monthly Payment</label>
                <div className={`${styles.paymentInput} ${styles.paymentShortInput}`} style={{
                  background: '#f0f9ff',
                  color: '#0369a1',
                  display: 'flex',
                  alignItems: 'center',
                  fontWeight: '600',
                  border: '1px solid #0ea5e9'
                }}>
                  ${estimatedPayment}
                </div>
              </div>
              <div className={styles.inputGroup}>
                <label className={styles.paymentInputLabel}>Estimated Monthly Payment W/O VSC</label>
                <div className={`${styles.paymentInput} ${styles.paymentShortInput}`} style={{
                  background: '#f8fafc',
                  color: '#64748b',
                  display: 'flex',
                  alignItems: 'center',
                  fontWeight: '500'
                }}>
                  ${estimatedPaymentWithoutVSC}
                </div>
              </div>
              <div className={styles.inputGroup}>
                <label className={styles.paymentInputLabel}>Difference</label>
                <div className={`${styles.paymentInput} ${styles.paymentShortInput}`} style={{
                  background: '#f0fdf4',
                  color: '#16a34a',
                  display: 'flex',
                  alignItems: 'center',
                  fontWeight: '600'
                }}>
                  ${paymentDifference}
                </div>
              </div>
            </div>
          </>
        ) : (
          /* Original simple calculator for tab 3 */
          <>
            <div className={styles.paymentInputRow}>
              <div className={styles.inputGroup}>
                <label className={styles.paymentInputLabel} htmlFor="input_98_82_tab3">VSC Price</label>
                <input
                  id="input_98_82_tab3"
                  name="input_82"
                  className={`${styles.paymentInput} ${styles.paymentShortInput}`}
                  type="text"
                  value={vscPrice}
                  onChange={handleVscPriceChange}
                  onBlur={handleVscPriceBlur}
                  onFocus={handleVscPriceFocus}
                  placeholder="$0.00"
                />
              </div>
              <div className={styles.inputGroup}>
                <label className={styles.paymentInputLabel} htmlFor="input_98_258_tab3">Loan Term (Months)</label>
                <input
                  id="input_98_258_tab3"
                  name="input_258"
                  className={`${styles.paymentInput} ${styles.paymentShortInput}`}
                  type="number"
                  min="1"
                  value={loanTerm}
                  onChange={e => setLoanTerm(e.target.value)}
                  placeholder="e.g. 60"
                />
              </div>
              <div className={styles.inputGroup}>
                <label className={styles.paymentInputLabel} htmlFor="input_98_34_tab3">Interest Rate (%)</label>
                <input
                  id="input_98_34_tab3"
                  name="input_34"
                  className={`${styles.paymentInput} ${styles.paymentShortInput}`}
                  type="number"
                  min="0"
                  step="0.01"
                  value={interestRate}
                  onChange={e => setInterestRate(e.target.value)}
                  placeholder="e.g. 5.5"
                />
              </div>
            </div>
            <div className={styles.estimatedPaymentRow}>
              Estimated Monthly Payment: ${estimatedPayment}
            </div>
          </>
        )}
      </div>
    </>
  );

  useEffect(() => {
    if (showDealerInfoForm) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [showDealerInfoForm]);

  useEffect(() => {
    // Only auto-scroll on mobile
    if (window.innerWidth < 800 && tabRefs.current && tabRefs.current.length) {
      const idx = tabOptions.findIndex(tab => tab.value === selectedOption);
      const activeTab = tabRefs.current[idx];
      if (activeTab && tabBarRef.current) {
        activeTab.scrollIntoView({ behavior: 'smooth', inline: 'center', block: 'nearest' });
      }
    }
  }, [selectedOption]);

  // Get current step status for indicators


  if (showDealerInfoForm) {
    return <OrderSummaryForm onBack={() => {
      setShowDealerInfoForm(false);
      // Navigate back to the previous step in the flow
      const prevStep = getPreviousStep();
      if (prevStep) {
        goToStep(prevStep);
      } else {
        // Fallback to the step before summary
        const currentSteps = getCurrentSteps();
        const summaryIndex = currentSteps.findIndex(step => step.id === 'summary');
        if (summaryIndex > 0) {
          goToStep(currentSteps[summaryIndex - 1].id);
        }
      }
    }} />;
  }



  if (currentStepId === 'adjust-limit') {
    return (
      <AdjustLimitForm
        onBack={() => {
          const prevStep = getPreviousStep();
          if (prevStep) {
            goToStep(prevStep);
          } else {
            goToStep('order');
          }
        }}
        onNext={() => {
          const nextStep = getNextStepInFlow('adjust-limit');
          if (nextStep) {
            goToStep(nextStep);
            if (nextStep === 'summary') {
              setShowDealerInfoForm(true);
            }
          }
        }}
        initialData={{
          coverage: vscSelection.contract,
          term: `${vscSelection.term} Month, Unlimited Mile`,
          deductible: parseInt(deductible),
          basePrice: parseFloat(vscPrice) || 1477.00,
          limitOfLiability: 10000.00,
          dealerMarkup: 178.00
        }}
      />
    );
  }

  return (
    <div className={styles.formContainer} style={{ maxWidth: 980, margin: "2rem auto", fontFamily, padding: '2.5rem 2rem', borderRadius: 16, boxShadow: '0 2px 12px rgba(0,0,0,0.07)', background: '#fff', border: '1px solid #e5e7eb' }}>
      <button
        className="shared-button secondary back-button"
        onClick={onBack}
        style={{ marginBottom: 18, fontSize: 15, padding: '0.5rem 1.3rem' }}
      >
        ← Back to VIN Decoder
      </button>
      <StepBar
        steps={getCurrentSteps()}
        currentStep={currentStepId}
        onStepClick={handleStepNavigation}
        allowNavigation={true}
      />

      <div className={styles.sectionCardCustom}>
        <h2 className={styles.sectionTitleCustom}>Vehicle & Order Information</h2>
        <div className={styles.dividerLineCustom} />
        <div className={styles.flexColGap16}>
          <div className={styles.centeredVinGroup}>
            <label className={styles.inputLabel}>VIN</label>
            {vinDecodeSuccess && (
              <div className={styles.vinSuccessMsgInline}>
                <svg width="18" height="18" fill="none" stroke="#16a34a" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                VIN validated successfully
              </div>
            )}
            <div className={styles.inputWithIcon}>
              <input
                id="input_98_36"
                name="input_36"
                value={vin}
                readOnly
                className={`shared-input ${styles.inputSmall} withIcon ${vinDecodeSuccess ? styles.vinInputHighlight : ''}`}
                disabled
              />
              <Icon name="vin" className={styles.inlineIcon18} />
            </div>
          </div>
          <div className={styles.vehicleInfoGrid}>
            <div className={styles.inputGroup}>
              <label className={styles.labelSmall}>Year</label>
              <div className={styles.inputWithIcon}>
                <input id="input_98_4" name="input_4" value={year} readOnly className={`shared-input ${styles.inputSmall}`} disabled />
                <Icon name="year" className={styles.inlineIcon18} />
              </div>
            </div>
            <div className={styles.inputGroup}>
              <label className={styles.labelSmall}>Make</label>
              <div className={styles.inputWithIcon}>
                <input id="input_98_5" name="input_5" value={make} readOnly className={`shared-input ${styles.inputSmall}`} disabled />
                <Icon name="make" className={styles.inlineIcon18} />
              </div>
            </div>
            <div className={styles.inputGroup}>
              <label className={styles.labelSmall}>Model</label>
              <div className={styles.inputWithIcon}>
                <input id="input_98_6" name="input_6" value={model} readOnly className={`shared-input ${styles.inputSmall}`} disabled />
                <Icon name="model" className={styles.inlineIcon18} />
              </div>
            </div>
            <div className={styles.inputGroup}>
              <label className={styles.labelSmall}>Trim</label>
              <div className={styles.inputWithIcon}>
                <input id="input_98_7" name="input_7" value={trim} readOnly className={`shared-input ${styles.inputSmall}`} disabled />
                <Icon name="trim" className={styles.inlineIcon18} />
              </div>
            </div>
            <div className={styles.inputGroup}>
              <label className={styles.labelSmall}>Odometer</label>
              <div className={styles.inputWithIcon}>
                <input id="input_98_138" name="input_138" value={odometer} readOnly className={`shared-input ${styles.inputSmall}`} disabled />
                <Icon name="odometer" className={styles.inlineIcon18} />
              </div>
            </div>
          </div>
        </div>

        {/* Hidden Vehicle Fields for Gravity Forms Compatibility */}
        <input type="hidden" name="input_11"  id="input_98_11" value={bodyStyle || ''} />  {/* Body Style - populated by VIN decoder */}
        <input type="hidden" name="input_8"  id="input_98_8" value={engine || ''} />   {/* Engine - populated by VIN decoder */}
        <input type="hidden" name="input_9"  id="input_98_9" value={cylinders || ''} />   {/* Cylinders - populated by VIN decoder */}
        <input type="hidden" name="input_10"  id="input_98_10" value={driveType || ''} />  {/* Drive Type - populated by VIN decoder */}
        <input type="hidden" name="input_37"  id="input_98_37" value={vehicleClass || ''} />  {/* Vehicle Class - populated by VIN decoder */}
        <input type="hidden" name="input_53"  id="input_98_53" value={odometer || ''} />  {/* Hidden Odometer - copy of main odometer */}

        {/* Hidden User/Demo Fields for Gravity Forms Compatibility */}
        <input type="hidden" name="input_120"  id="input_98_120" value="demo" />  {/* User Status */}
        <input type="hidden" name="input_425"  id="input_98_425" value="114" />   {/* User ID */}
        <input type="hidden" name="input_426"  id="input_98_426" value="Demo" />  {/* Demo Flag */}
      </div>
      <div>
        {/* Sticky header that appears when scrolling on mobile */}
        {isMobile && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            zIndex: 1000,
            background: 'rgba(214, 43, 37, 0.95)',
            color: '#fcf5f5ff',
            padding: '6px 12px',
            fontSize: 12,
            fontWeight: 500,
            textAlign: 'center',
            boxShadow: '0 1px 4px rgba(0,0,0,0.15)',
            transform: isScrolled ? 'translateY(0)' : 'translateY(-100%)',
            transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            opacity: isScrolled ? 1 : 0
          }}>
            {tabOptions.find(tab => tab.value === selectedOption)?.label}
          </div>
        )}

        {/* Tab bar for desktop, toggle group for mobile */}
        {isMobile ? (
          <div className={styles.toggleGroup}>
            {tabOptions.map(tab => (
              <button
                key={tab.value}
                type="button"
                className={
                  `${styles.toggleButton} ${selectedOption === tab.value ? styles.toggleButtonActive : ''}`
                }
                onClick={() => setSelectedOption(tab.value)}
              >
                {tab.label}
              </button>
            ))}
          </div>
        ) : (
          <div className={styles.orderTabs}>
            {tabOptions.map((tab) => (
              <button
                key={tab.value}
                type="button"
                className={`${styles.orderTab} ${selectedOption === tab.value ? styles.active : ''}`}
                onClick={() => setSelectedOption(tab.value)}
                tabIndex={0}
                title={tab.label}
              >
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        )}
        <div style={{ background: '#f9fafb', borderRadius: 8, padding: '0.3rem 0.3rem', margin: '2px 0 2px 0', minHeight: 40, display: 'flex', flexDirection: 'column', color: '#334155', fontSize: 14, border: '1px solid #e5e7eb', boxShadow: '0 1px 2px rgba(0,0,0,0.01)', fontFamily: 'Inter, Segoe UI, Arial, sans-serif' }}>
          {selectedOption === 'option1' && (
            <>
              <motion.div
                key="buyers-order-form"
                initial={{ opacity: 0, y: window.innerWidth <= 800 ? 32 : 0, x: window.innerWidth <= 800 ? 0 : 64 }}
                animate={{ opacity: 1, y: 0, x: 0 }}
                exit={{ opacity: 0, y: window.innerWidth <= 800 ? 32 : 0, x: window.innerWidth <= 800 ? 0 : -64 }}
                transition={{ duration: 0.32, ease: [0.4, 0, 0.2, 1] }}
                className={styles.optionContent}
              >
                <div style={{ background: '#f6f7fa', borderRadius: 6, padding: '0.7rem 0.8rem', margin: '4px 0 4px 0', display: 'flex', flexDirection: 'column', gap: '8px', border: '1px solid #e3e6ea', boxShadow: '0 1px 2px rgba(0,0,0,0.02)' }}>
                  {/* Mobile: 2 fields per row, Desktop: 4 fields per row */}
                  <div className={styles.pricingGrid}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <label htmlFor="input_98_71" style={{ fontSize: 13, color: '#64748b', fontWeight: 500, marginBottom: 2 }}>Cash Price of Vehicle</label>
                      <input id="input_98_71" name="input_71" type="text" className="shared-input" style={{ fontSize: 15, padding: '0.5rem 0.75rem', background: '#fff', border: '1px solid #e5e7eb' }} placeholder="$0.00" value={buyerOrder['input_71'] || ''} onChange={handleCurrencyFieldChange} onBlur={handleCurrencyFieldBlur} onFocus={handleCurrencyFieldFocus} autoComplete="off" />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <label htmlFor="input_98_123" style={{ fontSize: 13, color: '#64748b', fontWeight: 500, marginBottom: 2 }}>Accessories / Add-Ons</label>
                      <input id="input_98_123" name="input_123" type="text" className="shared-input" style={{ fontSize: 15, padding: '0.5rem 0.75rem', background: '#fff', border: '1px solid #e5e7eb' }} placeholder="$0.00" value={buyerOrder['input_123'] || ''} onChange={handleCurrencyFieldChange} onBlur={handleCurrencyFieldBlur} onFocus={handleCurrencyFieldFocus} autoComplete="off" />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <label htmlFor="input_98_124" style={{ fontSize: 13, color: '#64748b', fontWeight: 500, marginBottom: 2 }}>Documentary Fee</label>
                      <input id="input_98_124" name="input_124" type="text" className="shared-input" style={{ fontSize: 15, padding: '0.5rem 0.75rem', background: '#fff', border: '1px solid #e5e7eb' }} placeholder="$0.00" value={buyerOrder['input_124'] || ''} onChange={handleCurrencyFieldChange} onBlur={handleCurrencyFieldBlur} onFocus={handleCurrencyFieldFocus} autoComplete="off" />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <label htmlFor="input_98_173" style={{ fontSize: 13, color: '#64748b', fontWeight: 500, marginBottom: 2 }}>Other Expense</label>
                      <input id="input_98_173" name="input_173" type="text" className="shared-input" style={{ fontSize: 15, padding: '0.5rem 0.75rem', background: '#fff', border: '1px solid #e5e7eb' }} placeholder="$0.00" value={buyerOrder['input_173'] || ''} onChange={handleCurrencyFieldChange} onBlur={handleCurrencyFieldBlur} onFocus={handleCurrencyFieldFocus} autoComplete="off" />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <label htmlFor="input_98_117" style={{ fontSize: 13, color: '#64748b', fontWeight: 500, marginBottom: 2 }}>Trade-In Value</label>
                      <input id="input_98_117" name="input_117" type="text" className="shared-input" style={{ fontSize: 15, padding: '0.5rem 0.75rem', background: '#fff', border: '1px solid #e5e7eb' }} placeholder="$0.00" value={buyerOrder['input_117'] || ''} onChange={handleCurrencyFieldChange} onBlur={handleCurrencyFieldBlur} onFocus={handleCurrencyFieldFocus} autoComplete="off" />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <label htmlFor="input_98_126" style={{ fontSize: 13, color: '#64748b', fontWeight: 500, marginBottom: 2 }}>Balance Owed on Trade In</label>
                      <input id="input_98_126" name="input_126" type="text" className="shared-input" style={{ fontSize: 15, padding: '0.5rem 0.75rem', background: '#fff', border: '1px solid #e5e7eb' }} placeholder="$0.00" value={buyerOrder['input_126'] || ''} onChange={handleCurrencyFieldChange} onBlur={handleCurrencyFieldBlur} onFocus={handleCurrencyFieldFocus} autoComplete="off" />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <label htmlFor="input_98_127" style={{ fontSize: 13, color: '#64748b', fontWeight: 500, marginBottom: 2 }}>Net Trade In Allowance</label>
                      <input id="input_98_127" name="input_127" type="text" className="shared-input" style={{ fontSize: 15, padding: '0.5rem 0.75rem', background: '#fff', border: '1px solid #e5e7eb' }} placeholder="$0.00" value={buyerOrder['input_127'] || ''} onChange={handleCurrencyFieldChange} onBlur={handleCurrencyFieldBlur} onFocus={handleCurrencyFieldFocus} autoComplete="off" />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <label htmlFor="input_98_128" style={{ fontSize: 13, color: '#64748b', fontWeight: 500, marginBottom: 2 }}>Cash Deposit</label>
                      <input id="input_98_128" name="input_128" type="text" className="shared-input" style={{ fontSize: 15, padding: '0.5rem 0.75rem', background: '#fff', border: '1px solid #e5e7eb' }} placeholder="$0.00" value={buyerOrder['input_128'] || ''} onChange={handleCurrencyFieldChange} onBlur={handleCurrencyFieldBlur} onFocus={handleCurrencyFieldFocus} autoComplete="off" />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <label htmlFor="input_98_72" style={{ fontSize: 13, color: '#64748b', fontWeight: 500, marginBottom: 2 }}>Down Payment</label>
                      <input id="input_98_72" name="input_72" type="text" className="shared-input" style={{ fontSize: 15, padding: '0.5rem 0.75rem', background: '#fff', border: '1px solid #e5e7eb' }} placeholder="$0.00" value={buyerOrder['input_72'] || ''} onChange={handleCurrencyFieldChange} onBlur={handleCurrencyFieldBlur} onFocus={handleCurrencyFieldFocus} autoComplete="off" />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <label htmlFor="input_98_129" style={{ fontSize: 13, color: '#64748b', fontWeight: 500, marginBottom: 2 }}>Rebate</label>
                      <input id="input_98_129" name="input_129" type="text" className="shared-input" style={{ fontSize: 15, padding: '0.5rem 0.75rem', background: '#fff', border: '1px solid #e5e7eb' }} placeholder="$0.00" value={buyerOrder['input_129'] || ''} onChange={handleCurrencyFieldChange} onBlur={handleCurrencyFieldBlur} onFocus={handleCurrencyFieldFocus} autoComplete="off" />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <label htmlFor="input_98_174" style={{ fontSize: 13, color: '#64748b', fontWeight: 500, marginBottom: 2 }}>Other Credit</label>
                      <input id="input_98_174" name="input_174" type="text" className="shared-input" style={{ fontSize: 15, padding: '0.5rem 0.75rem', background: '#fff', border: '1px solid #e5e7eb' }} placeholder="$0.00" value={buyerOrder['input_174'] || ''} onChange={handleCurrencyFieldChange} onBlur={handleCurrencyFieldBlur} onFocus={handleCurrencyFieldFocus} autoComplete="off" />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <label htmlFor="input_98_133" style={{ fontSize: 13, color: '#64748b', fontWeight: 500, marginBottom: 2 }}>Total Loan Amount</label>
                      <input id="input_98_133" name="input_133" type="text" className="shared-input" style={{ fontSize: 15, padding: '0.5rem 0.75rem', background: '#fff', border: '1px solid #e5e7eb' }} placeholder="$0.00" value={buyerOrder['input_133'] || ''} onChange={handleCurrencyFieldChange} onBlur={handleCurrencyFieldBlur} onFocus={handleCurrencyFieldFocus} autoComplete="off" />
                    </div>
                  </div>
                </div>
                {tabData}
                {/* Lower Limit of Liability toggle for option1 */}
                <div style={{ display: 'flex', gap: 10, margin: '18px 0 0 0', justifyContent: 'left' }}>
                  <button
                    type="button"
                    aria-pressed={activeSpecial === 'lowerLimit'}
                    className={`shared-button secondary back-button${activeSpecial === 'lowerLimit' || lowerLimitFocused ? ' lower-limit-active' : ''}`}
                    onClick={handleLowerLimitToggle}
                    onFocus={() => setLowerLimitFocused(true)}
                    onBlur={() => setLowerLimitFocused(false)}
                    style={{
                      fontSize: 14,
                      padding: '0.5rem 0.75rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                      background: activeSpecial === 'lowerLimit' ? '#fef2f2' : '#fff',
                      borderColor: activeSpecial === 'lowerLimit' ? '#dc2626' : '#e5e7eb'
                    }}
                  >
                    <span style={{ display: 'inline-flex', alignItems: 'center', justifyContent: 'center', width: 18, height: 18 }}>
                      {activeSpecial === 'lowerLimit' ? (
                        <svg width="18" height="18" viewBox="0 0 18 18" fill="#64748b" stroke="#64748b" strokeWidth="2" style={{ borderRadius: 4, background: '#fff' }}>
                          <rect x="2" y="2" width="14" height="14" rx="4" fill="#64748b" />
                          <polyline points="5,10 8,13 13,6" fill="none" stroke="#fff" strokeWidth="2.2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                      ) : (
                        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="#64748b" strokeWidth="2" style={{ borderRadius: 4, background: '#fff' }}>
                          <rect x="2" y="2" width="14" height="14" rx="4" fill="#fff" stroke="#cbd5e1" strokeWidth="1.5" />
                        </svg>
                      )}
                    </span>
                    Lower Limit of Liability
                  </button>
                </div>
                {/* Show yellow message if Lower Limit of Liability is active */}
                {/* {activeSpecial === 'lowerLimit' && (
                <div style={{ background: '#fef9c3', border: '1.5px solid #fde68a', borderRadius: 10, padding: '1.2rem 1.5rem', color: '#b45309', fontWeight: 600, fontSize: 16, textAlign: 'center', margin: '2.5rem 0' }}>
                  You have chosen Lower Limit of Liability. Please follow up with the customer as needed.
                </div>
              )} */}
              </motion.div>
            </>
          )}
          {selectedOption === 'option2' && (
            <>
              <motion.div
                key="loan-amount-form"
                initial={{ opacity: 0, y: window.innerWidth <= 800 ? 32 : 0, x: window.innerWidth <= 800 ? 0 : 64 }}
                animate={{ opacity: 1, y: 0, x: 0 }}
                exit={{ opacity: 0, y: window.innerWidth <= 800 ? 32 : 0, x: window.innerWidth <= 800 ? 0 : -64 }}
                transition={{ duration: 0.32, ease: [0.4, 0, 0.2, 1] }}
                className={styles.optionContent}
              >
                <div style={{ background: '#f9fafb', borderRadius: 8, padding: '0.7rem 0.8rem', margin: '4px 0 4px 0', minHeight: 40, display: 'flex', flexDirection: 'column', color: '#334155', fontSize: 15, border: '1px solid #e5e7eb', boxShadow: '0 1px 2px rgba(0,0,0,0.01)' }}>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 2, maxWidth: 340 }}>
                    <label htmlFor="input_98_140" style={{ fontSize: 13, color: '#64748b', fontWeight: 500, marginBottom: 2 }}>Total Loan Amount</label>
                    <input
                      id="input_98_140"
                      name="input_140"
                      type="text"
                      className="shared-input"
                      style={{ fontSize: 15, padding: '0.5rem 0.75rem', background: '#fff', border: '1px solid #e5e7eb' }}
                      placeholder="$0.00"
                      value={buyerOrder['input_140'] || ''}
                      onChange={handleCurrencyFieldChange}
                      onBlur={handleCurrencyFieldBlur}
                      onFocus={handleCurrencyFieldFocus}
                      autoComplete="off"
                    />
                  </div>
                </div>
                {tabData}
                {/* Lower Limit of Liability toggle for option2 */}
                <div style={{ display: 'flex', gap: 10, margin: '18px 0 0 0', justifyContent: 'left' }}>
                  <button
                    type="button"
                    aria-pressed={activeSpecial === 'lowerLimit'}
                    className={`shared-button secondary back-button${activeSpecial === 'lowerLimit' || lowerLimitFocused ? ' lower-limit-active' : ''}`}
                    onClick={handleLowerLimitToggle}
                    onFocus={() => setLowerLimitFocused(true)}
                    onBlur={() => setLowerLimitFocused(false)}
                    style={{
                      fontSize: 14,
                      padding: '0.5rem 0.75rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                      background: activeSpecial === 'lowerLimit' ? '#fef2f2' : '#fff',
                      borderColor: activeSpecial === 'lowerLimit' ? '#dc2626' : '#e5e7eb'
                    }}
                  >
                    <span style={{ display: 'inline-flex', alignItems: 'center', justifyContent: 'center', width: 18, height: 18 }}>
                      {activeSpecial === 'lowerLimit' ? (
                        <svg width="18" height="18" viewBox="0 0 18 18" fill="#64748b" stroke="#64748b" strokeWidth="2" style={{ borderRadius: 4, background: '#fff' }}>
                          <rect x="2" y="2" width="14" height="14" rx="4" fill="#64748b" />
                          <polyline points="5,10 8,13 13,6" fill="none" stroke="#fff" strokeWidth="2.2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                      ) : (
                        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="#64748b" strokeWidth="2" style={{ borderRadius: 4, background: '#fff' }}>
                          <rect x="2" y="2" width="14" height="14" rx="4" fill="#fff" stroke="#cbd5e1" strokeWidth="1.5" />
                        </svg>
                      )}
                    </span>
                    Lower Limit of Liability
                  </button>
                </div>
                {/* Show yellow message if Lower Limit of Liability is active */}
                {/* {activeSpecial === 'lowerLimit' && (
                <div style={{ background: '#fef9c3', border: '1.5px solid #fde68a', borderRadius: 10, padding: '1.2rem 1.5rem', color: '#b45309', fontWeight: 600, fontSize: 16, textAlign: 'center', margin: '2.5rem 0' }}>
                  You have chosen Lower Limit of Liability. Please follow up with the customer as needed.
                </div>
              )} */}
              </motion.div>
            </>
          )}
          {selectedOption === 'option3' && (
            <>
              <motion.div
                key="vsc-details-form"
                initial={{ opacity: 0, y: window.innerWidth <= 800 ? 32 : 0, x: window.innerWidth <= 800 ? 0 : 64 }}
                animate={{ opacity: 1, y: 0, x: 0 }}
                exit={{ opacity: 0, y: window.innerWidth <= 800 ? 32 : 0, x: window.innerWidth <= 800 ? 0 : -64 }}
                transition={{ duration: 0.32, ease: [0.4, 0, 0.2, 1] }}
                className={styles.optionContent}
              >
                {tabData}
                {/* Lower Limit of Liability toggle only for VSC Details tab */}
                <div style={{ display: 'flex', gap: 10, margin: '18px 0 0 0', justifyContent: 'left' }}>
                  <button
                    type="button"
                    aria-pressed={activeSpecial === 'lowerLimit'}
                    className={`shared-button secondary back-button${activeSpecial === 'lowerLimit' || lowerLimitFocused ? ' lower-limit-active' : ''}`}
                    onClick={handleLowerLimitToggle}
                    onFocus={() => setLowerLimitFocused(true)}
                    onBlur={() => setLowerLimitFocused(false)}
                    style={{
                      fontSize: 14,
                      padding: '0.5rem 0.75rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                      background: activeSpecial === 'lowerLimit' ? '#fef2f2' : '#fff',
                      borderColor: activeSpecial === 'lowerLimit' ? '#dc2626' : '#e5e7eb'
                    }}
                  >
                    <span style={{ display: 'inline-flex', alignItems: 'center', justifyContent: 'center', width: 18, height: 18 }}>
                      {activeSpecial === 'lowerLimit' ? (
                        <svg width="18" height="18" viewBox="0 0 18 18" fill="#64748b" stroke="#64748b" strokeWidth="2" style={{ borderRadius: 4, background: '#fff' }}>
                          <rect x="2" y="2" width="14" height="14" rx="4" fill="#64748b" />
                          <polyline points="5,10 8,13 13,6" fill="none" stroke="#fff" strokeWidth="2.2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                      ) : (
                        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="#64748b" strokeWidth="2" style={{ borderRadius: 4, background: '#fff' }}>
                          <rect x="2" y="2" width="14" height="14" rx="4" fill="#fff" stroke="#cbd5e1" strokeWidth="1.5" />
                        </svg>
                      )}
                    </span>
                    Lower Limit of Liability
                  </button>
                </div>
                {/* Show yellow message if Lower Limit of Liability is active */}
                {/* {activeSpecial === 'lowerLimit' && (
                <div style={{ background: '#fef9c3', border: '1.5px solid #fde68a', borderRadius: 10, padding: '1.2rem 1.5rem', color: '#b45309', fontWeight: 600, fontSize: 16, textAlign: 'center', margin: '2.5rem 0' }}>
                  You have chosen Lower Limit of Liability. Please follow up with the customer as needed.
                </div>
              )} */}
              </motion.div>
            </>
          )}
          {selectedOption === 'option4' && (
            <motion.div
              key="recommend-contract"
              initial={{ opacity: 0, y: window.innerWidth <= 800 ? 32 : 0, x: window.innerWidth <= 800 ? 0 : 64 }}
              animate={{ opacity: 1, y: 0, x: 0 }}
              exit={{ opacity: 0, y: window.innerWidth <= 800 ? 32 : 0, x: window.innerWidth <= 800 ? 0 : -64 }}
              transition={{ duration: 0.32, ease: [0.4, 0, 0.2, 1] }}
              className={styles.optionContent}
            >

              {/* Recommend Contract Form Content */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: isMobile ? '1fr' : '1fr 1fr',
                gap: isMobile ? '1.5rem' : '2rem',
                marginBottom: '2rem',
                background: '#f8fafc',
                padding: isMobile ? '1rem' : '1.5rem',
                borderRadius: '12px',
                border: '1px solid #e2e8f0',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
              }}>
                {/* Left Column */}
                <div>
                  <div style={{ marginBottom: '1.5rem' }}>
                    <label style={recommendLabelStyle}>
                      Retail Price *
                    </label>
                    <input
                      type="text"
                      placeholder="Enter retail price (e.g., $2,500.00)"
                      value={recommendData.retailPrice}
                      onChange={handleRetailPriceChange}
                      onFocus={(e) => {
                        e.target.style.borderColor = '#2563eb';
                        handleRetailPriceFocus();
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = '#e2e8f0';
                        handleRetailPriceBlur();
                      }}
                      style={recommendInputStyle}
                    />
                  </div>

                  <div style={{ marginBottom: '1.5rem' }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      marginBottom: '0.5rem'
                    }}>
                      <label style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        cursor: 'pointer',
                        fontWeight: 500,
                        color: '#374151',
                        fontSize: '15px'
                      }}>
                        <span style={{
                          display: 'inline-block',
                          width: 16,
                          height: 16,
                          border: '2px solid #cbd5e1',
                          borderRadius: 3,
                          backgroundColor: recommendData.showMarkup ? '#2563eb' : 'transparent',
                          borderColor: recommendData.showMarkup ? '#2563eb' : '#cbd5e1',
                          position: 'relative',
                          transition: 'all 0.2s ease'
                        }}>
                          {recommendData.showMarkup && (
                            <svg
                              viewBox="0 0 16 16"
                              fill="none"
                              stroke="white"
                              strokeWidth="2.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              style={{
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: 'translate(-50%, -50%)',
                                width: 10,
                                height: 10
                              }}
                            >
                              <polyline points="3,8 6,11 13,4" />
                            </svg>
                          )}
                        </span>
                        <input
                          type="checkbox"
                          checked={recommendData.showMarkup}
                          onChange={(e) => setRecommendData(prev => ({ ...prev, showMarkup: e.target.checked }))}
                          style={{
                            position: 'absolute',
                            opacity: 0,
                            width: 0,
                            height: 0
                          }}
                        />
                        Show Markup
                      </label>
                    </div>
                    {recommendData.showMarkup && (
                      <div style={{ marginTop: '0.75rem' }}>
                        <input
                          type="text"
                          placeholder="Enter markup amount (e.g., $150.00)"
                          value={recommendData.markupAmount}
                          onChange={handleMarkupAmountChange}
                          onFocus={(e) => {
                            e.target.style.borderColor = '#2563eb';
                            handleMarkupAmountFocus();
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = '#e2e8f0';
                            handleMarkupAmountBlur();
                          }}
                          style={recommendInputStyle}
                        />
                      </div>
                    )}
                  </div>
                </div>

                {/* Right Column */}
                <div>
                  {/* Only show suggestion fields after retail price is entered */}
                  {recommendData.retailPrice && (
                    <>
                      <div style={{ marginBottom: '1.5rem' }}>
                        <label style={recommendLabelStyle}>
                          Suggested Plan
                        </label>
                        <div style={{
                          fontSize: '16px',
                          fontWeight: '600',
                          color: '#1f2937',
                          marginTop: '0.5rem'
                        }}>
                          {recommendData.suggestedPlan || 'Industry Best'}
                        </div>
                      </div>

                      <div style={{ marginBottom: '1.5rem' }}>
                        <label style={recommendLabelStyle}>
                          Suggested Term (months)
                        </label>
                        <div style={{
                          fontSize: '16px',
                          fontWeight: '600',
                          color: '#1f2937',
                          marginTop: '0.5rem'
                        }}>
                          {recommendData.suggestedTerm ? `${recommendData.suggestedTerm} months` : '60 months'}
                        </div>
                      </div>

                      <div style={{ marginBottom: '1.5rem' }}>
                        <label style={recommendLabelStyle}>
                          Suggested Deductible
                        </label>
                        <div style={{
                          fontSize: '16px',
                          fontWeight: '600',
                          color: '#1f2937',
                          marginTop: '0.5rem'
                        }}>
                          {recommendData.suggestedDeductible || '$200'}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Lower Limit of Liability toggle for option4 */}
              <div style={{ display: 'flex', gap: 10, margin: '18px 0 0 0', justifyContent: 'left' }}>
                <button
                  type="button"
                  aria-pressed={recommendLowLimit}
                  className={`shared-button secondary back-button${recommendLowLimit || lowerLimitFocused ? ' lower-limit-active' : ''}`}
                  onClick={() => {
                    setRecommendLowLimit(!recommendLowLimit);
                    updateStepsOnly(selectedOption, !recommendLowLimit);
                  }}
                  onFocus={() => setLowerLimitFocused(true)}
                  onBlur={() => setLowerLimitFocused(false)}
                  style={{
                    fontSize: isMobile ? 14 : 15,
                    padding: isMobile ? '0.4rem 1rem' : '0.5rem 1.3rem',
                    marginBottom: 0,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}
                >
                  <span style={{
                    display: 'inline-block',
                    width: 16,
                    height: 16,
                    border: '2px solid currentColor',
                    borderRadius: 3,
                    backgroundColor: recommendLowLimit ? 'currentColor' : 'transparent',
                    position: 'relative'
                  }}>
                    {recommendLowLimit && (
                      <svg
                        viewBox="0 0 16 16"
                        fill="none"
                        stroke="white"
                        strokeWidth="2.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        style={{
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)',
                          width: 10,
                          height: 10
                        }}
                      >
                        <polyline points="3,8 6,11 13,4" />
                      </svg>
                    )}
                  </span>
                  Low Limit of Liability
                </button>
              </div>
            </motion.div>
          )}
          {selectedOption === 'option5' && (
            <motion.div
              key="price-match"
              initial={{ opacity: 0, y: window.innerWidth <= 800 ? 32 : 0, x: window.innerWidth <= 800 ? 0 : 64 }}
              animate={{ opacity: 1, y: 0, x: 0 }}
              exit={{ opacity: 0, y: window.innerWidth <= 800 ? 32 : 0, x: window.innerWidth <= 800 ? 0 : -64 }}
              transition={{ duration: 0.32, ease: [0.4, 0, 0.2, 1] }}
              className={styles.optionContent}
            >
              <div style={{ background: '#f6f7fa', borderRadius: 6, padding: '1rem', border: '1px solid #e3e6ea' }}>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: window.innerWidth <= 800 ? '1fr' : '1fr 1fr',
                  gap: window.innerWidth <= 800 ? '1rem' : '2rem',
                  marginBottom: '1.5rem'
                }}>
                  {/* Competitor Coverage */}
                  <div>
                    <h3 style={{
                      fontSize: window.innerWidth <= 800 ? 13 : 14,
                      fontWeight: 600,
                      marginBottom: 8,
                      color: '#334155'
                    }}>
                      Competitor Coverage <span style={{ color: '#dc2626' }}>*Required</span>
                    </h3>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                      <label style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 6,
                        fontSize: window.innerWidth <= 800 ? 12 : 13,
                        padding: window.innerWidth <= 800 ? '0.5rem 0.75rem' : '0',
                        background: window.innerWidth <= 800 ? '#ffffff' : 'transparent',
                        border: window.innerWidth <= 800 ? '1px solid #e2e8f0' : 'none',
                        borderRadius: window.innerWidth <= 800 ? 6 : 0
                      }}>
                        <input
                          type="radio"
                          name="input_415"
                          value="exclusionary"
                          checked={priceMatchData.competitorCoverage === 'exclusionary'}
                          onChange={(e) => setPriceMatchData({ ...priceMatchData, competitorCoverage: e.target.value })}
                          className="custom-radio"
                        />
                        Exclusionary
                      </label>
                    </div>
                  </div>

                  {/* Competitor Terms */}
                  <div>
                    <h3 style={{
                      fontSize: window.innerWidth <= 800 ? 13 : 14,
                      fontWeight: 600,
                      marginBottom: 8,
                      color: '#334155'
                    }}>
                      Competitor Terms <span style={{ color: '#dc2626' }}>*Required</span>
                    </h3>
                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: window.innerWidth <= 800 ? 'repeat(2, 1fr)' : 'repeat(2, 1fr)',
                      gap: window.innerWidth <= 800 ? 6 : 4
                    }}>
                      {['12', '24', '36', '48', '60', '72', '84', '96', '108', '120'].map(term => (
                        <label key={term} style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 6,
                          fontSize: window.innerWidth <= 800 ? 12 : 13,
                          padding: window.innerWidth <= 800 ? '0.5rem 0.75rem' : '0',
                          background: window.innerWidth <= 800 ? '#ffffff' : 'transparent',
                          border: window.innerWidth <= 800 ? '1px solid #e2e8f0' : 'none',
                          borderRadius: window.innerWidth <= 800 ? 6 : 0,
                          marginBottom: window.innerWidth <= 800 ? 4 : 0
                        }}>
                          <input
                            type="radio"
                            name="input_13"
                            value={term}
                            checked={priceMatchData.competitorTerm === term}
                            onChange={(e) => setPriceMatchData({ ...priceMatchData, competitorTerm: e.target.value })}
                            className="custom-radio"
                          />
                          {term}/Unlimited
                        </label>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Form Fields */}
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: window.innerWidth <= 800 ? '1fr' : '1fr 1fr',
                  gap: '1rem',
                  marginBottom: '1rem'
                }}>
                  <div>
                    <label style={{
                      fontSize: window.innerWidth <= 800 ? 12 : 13,
                      fontWeight: 500,
                      color: '#64748b',
                      marginBottom: 4,
                      display: 'block'
                    }}>
                      Provider Name <span style={{ color: '#dc2626' }}>*Required</span>
                    </label>
                    <input
                      type="text"
                      className="shared-input"
                      style={inputStyle}
                      value={priceMatchData.providerName}
                      onChange={(e) => setPriceMatchData({ ...priceMatchData, providerName: e.target.value })}
                    />
                  </div>
                  <div>
                    <label style={{
                      fontSize: window.innerWidth <= 800 ? 12 : 13,
                      fontWeight: 500,
                      color: '#64748b',
                      marginBottom: 4,
                      display: 'block'
                    }}>
                      Product Name <span style={{ color: '#dc2626' }}>*Required</span>
                    </label>
                    <input
                      type="text"
                      className="shared-input"
                      style={inputStyle}
                      value={priceMatchData.productName}
                      onChange={(e) => setPriceMatchData({ ...priceMatchData, productName: e.target.value })}
                    />
                  </div>
                </div>

                {/* File Upload */}
                <div style={{ marginBottom: '1rem' }}>
                  <label style={{
                    fontSize: window.innerWidth <= 800 ? 12 : 13,
                    fontWeight: 500,
                    color: '#64748b',
                    marginBottom: 4,
                    display: 'block'
                  }}>
                    Upload Screenshot <span style={{ color: '#dc2626' }}>*Required</span>
                  </label>
                  <div style={{
                    border: '2px dashed #cbd5e1',
                    borderRadius: 6,
                    padding: window.innerWidth <= 800 ? '1.5rem 1rem' : '2rem',
                    textAlign: 'center',
                    background: '#f8fafc'
                  }}>
                    <input
                      type="file"
                      id="screenshot-upload"
                      accept="image/*,.pdf"
                      multiple
                      style={{ display: 'none' }}
                      onChange={(e) => {
                        const files = Array.from(e.target.files || []);
                        // Handle file upload logic here
                        console.log('Selected files:', files);
                      }}
                    />
                    <div style={{
                      fontSize: window.innerWidth <= 800 ? 13 : 14,
                      color: '#64748b',
                      marginBottom: 8
                    }}>
                      Drop files here or
                    </div>
                    <button
                      type="button"
                      onClick={() => document.getElementById('screenshot-upload')?.click()}
                      style={{
                        background: '#374151',
                        color: 'white',
                        padding: window.innerWidth <= 800 ? '0.4rem 0.8rem' : '0.5rem 1rem',
                        borderRadius: 4,
                        border: 'none',
                        fontSize: window.innerWidth <= 800 ? 12 : 13,
                        cursor: 'pointer'
                      }}
                    >
                      Select Files
                    </button>
                    <div style={{
                      fontSize: window.innerWidth <= 800 ? 11 : 12,
                      color: '#9ca3af',
                      marginTop: 4
                    }}>
                      Max file size 8 GB
                    </div>
                  </div>
                </div>

                {/* Price Fields */}
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: window.innerWidth <= 800 ? '1fr' : '1fr 1fr 1fr',
                  gap: '1rem'
                }}>
                  <div>
                    <label style={{
                      fontSize: window.innerWidth <= 800 ? 12 : 13,
                      fontWeight: 500,
                      color: '#64748b',
                      marginBottom: 4,
                      display: 'block'
                    }}>
                      Competitors dealer cost <span style={{ color: '#dc2626' }}>*Required</span>
                    </label>
                    <input
                      type="text"
                      className="shared-input"
                      style={inputStyle}
                      value={priceMatchData.dealerCost}
                      onChange={(e) => {
                        const value = validateNumericInput(e.target.value, true);
                        setPriceMatchData({ ...priceMatchData, dealerCost: value });
                      }}
                      onBlur={(e) => {
                        if (e.target.value) {
                          const formatted = formatCurrencyInput(e.target.value);
                          setPriceMatchData({ ...priceMatchData, dealerCost: formatted });
                        }
                      }}
                      onFocus={(e) => {
                        const numericValue = removeCurrencyFormatting(e.target.value);
                        setPriceMatchData({ ...priceMatchData, dealerCost: numericValue });
                      }}
                      placeholder="0.00"
                    />
                  </div>
                  <div>
                    <label style={{
                      fontSize: window.innerWidth <= 800 ? 12 : 13,
                      fontWeight: 500,
                      color: '#64748b',
                      marginBottom: 4,
                      display: 'block'
                    }}>
                      Your markup <span style={{ color: '#dc2626' }}>*Required</span>
                    </label>
                    <input
                      type="text"
                      className="shared-input"
                      style={inputStyle}
                      value={priceMatchData.markup}
                      onChange={(e) => {
                        const value = validateNumericInput(e.target.value, true);
                        setPriceMatchData({ ...priceMatchData, markup: value });
                      }}
                      onBlur={(e) => {
                        if (e.target.value) {
                          const formatted = formatCurrencyInput(e.target.value);
                          setPriceMatchData({ ...priceMatchData, markup: formatted });
                        }
                      }}
                      onFocus={(e) => {
                        const numericValue = removeCurrencyFormatting(e.target.value);
                        setPriceMatchData({ ...priceMatchData, markup: numericValue });
                      }}
                      placeholder="0.00"
                    />
                  </div>
                  <div>
                    <label style={{
                      fontSize: window.innerWidth <= 800 ? 12 : 13,
                      fontWeight: 500,
                      color: '#64748b',
                      marginBottom: 4,
                      display: 'block'
                    }}>
                      Total price of the contract <span style={{ color: '#dc2626' }}>*Required</span>
                    </label>
                    <div style={{
                      ...inputStyle,
                      background: '#f1f5f9',
                      color: '#64748b',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      ${priceMatchData.totalPrice}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>
      <div style={{
        marginTop: 24,
        display: 'flex',
        gap: 14,
        justifyContent: 'flex-end',
        alignItems: 'center'
      }}>
        <button
          className="shared-button primary"
          style={{
            fontSize: window.innerWidth <= 800 ? 14 : 16,
            padding: window.innerWidth <= 800 ? '0.5rem 1rem' : '0.7rem 1.5rem'
          }}
          onClick={handleProceedToNext}
        >
          Next
        </button>
        <button
          className="shared-button tertiary"
          style={{
            fontSize: window.innerWidth <= 800 ? 14 : 16,
            padding: window.innerWidth <= 800 ? '0.5rem 1rem' : '0.7rem 1.5rem'
          }}
        >
          <span role="img" aria-label="save">💾</span>
          Save & Continue
        </button>
      </div>
    </div>
  );
};

export default OrderFormPage;


