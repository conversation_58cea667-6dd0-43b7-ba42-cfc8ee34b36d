import React from 'react';

interface StepProgressBarProps {
  progress: number;
  height?: number;
  showPercentage?: boolean;
}

const StepProgressBar: React.FC<StepProgressBarProps> = ({
  progress,
  height = 4,
  showPercentage = false
}) => {
  const clampedProgress = Math.max(0, Math.min(100, progress));

  return (
    <div style={{
      width: '100%',
      marginBottom: '1rem'
    }}>
      <div style={{
        width: '100%',
        height: height,
        backgroundColor: '#e5e7eb',
        borderRadius: height / 2,
        overflow: 'hidden',
        position: 'relative'
      }}>
        <div style={{
          width: `${clampedProgress}%`,
          height: '100%',
          background: 'linear-gradient(90deg, #16a34a, #22c55e)',
          borderRadius: height / 2,
          transition: 'width 0.3s ease',
          boxShadow: '0 1px 3px rgba(22, 163, 74, 0.2)'
        }} />
      </div>
      {showPercentage && (
        <div style={{
          textAlign: 'center',
          fontSize: 12,
          color: '#64748b',
          marginTop: 4,
          fontWeight: 500
        }}>
          {Math.round(clampedProgress)}% Complete
        </div>
      )}
    </div>
  );
};

export default StepProgressBar; 