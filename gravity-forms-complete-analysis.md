# 🔍 **Complete Gravity Forms Field Analysis**

## **📊 Field Mapping Summary**

Based on comprehensive analysis of the Gravity Forms HTML, here's the complete field mapping:

### **🔧 Input Fields with Labels**

| **Field ID** | **Input Name** | **Input ID** | **Label Text** | **Type** | **Hidden?** | **Default Value** |
|--------------|----------------|--------------|----------------|----------|-------------|-------------------|
| **User/Demo Fields** |
| 120 | `input_120` | `input_98_120` | User Status | text | ❌ | "demo" |
| 425 | `input_425` | `input_98_425` | User ID | text | ❌ | "114" |
| 426 | `input_426` | `input_98_426` | Demo? | text | ❌ | "Demo" |
| **Vehicle Information** |
| 36 | `input_36` | `input_98_36` | VIN | text | ❌ | "JTMBFREV5JJ213506" |
| 4 | `input_4` | `input_98_4` | Year | text | ❌ | "2018" |
| 5 | `input_5` | `input_98_5` | Make | text | ❌ | "TOYOTA" |
| 6 | `input_6` | `input_98_6` | Model | text | ❌ | "RAV4" |
| 7 | `input_7` | `input_98_7` | Trim | text | ❌ | "LE" |
| 138 | `input_138` | `input_98_138` | Vehicle Odometer | number | ❌ | "85000" |
| **Hidden Vehicle Fields** |
| 11 | `input_11` | `input_98_11` | Series | text | ✅ | "ASA44L/ASA42L/AVA44L" |
| 8 | `input_8` | `input_98_8` | Engine | text | ✅ | "2AR-FE" |
| 9 | `input_9` | `input_98_9` | Transmission | text | ✅ | "6" |
| 10 | `input_10` | `input_98_10` | Drive Type | text | ✅ | "4WD/4-Wheel Drive/4x4" |
| 53 | `input_53` | `input_98_53` | Hidden Odometer | text | ✅ | "" |
| 37 | `input_37` | `input_98_37` | Class: | text | ✅ | "Class 1" |
| **Order Method Fields** |
| 411 | `input_411` | `input_98_411` | Favorite Ordering Method (hidden) | text | ✅ | "Generating a Buyers Order, Entering Loan Amount Only, VSC Details Only, Recommend a Contract" |
| 413 | `input_413` | `choice_98_413_*` | Order Method Selection | radio | ❌ | Multiple options |
| 414 | `input_414` | `input_98_414` | continue by: indicator (hidden) | text | ✅ | "" |
| 137 | `input_137` | `choice_98_137_*` | Order Method Confirmation | radio | ❌ | Multiple options |
| **Pricing Fields** |
| 71 | `input_71` | `input_98_71` | Cash Price of Vehicle | text | ❌ | "$0.00" |
| 123 | `input_123` | `input_98_123` | Accessories / Add-Ons | text | ❌ | "$0.00" |
| 124 | `input_124` | `input_98_124` | Documentary Fee | text | ❌ | "$0.00" |
| 173 | `input_173` | `input_98_173` | Other Expense | text | ❌ | "$0.00" |
| 117 | `input_117` | `input_98_117` | Trade-In Value | text | ❌ | "$0.00" |
| 126 | `input_126` | `input_98_126` | Balance Owed on Trade In | text | ❌ | "$0.00" |
| 127 | `input_127` | `input_98_127` | Net Trade in Allowance | text | ❌ | "" |
| 128 | `input_128` | `input_98_128` | Cash Deposit | text | ❌ | "$0.00" |
| 72 | `input_72` | `input_98_72` | Down Payment | text | ❌ | "$0.00" |
| 129 | `input_129` | `input_98_129` | Rebate | text | ❌ | "$0.00" |
| 174 | `input_174` | `input_98_174` | Other Credit | text | ❌ | "$0.00" |
| 133 | `input_133` | `input_98_133` | Total Loan Amount | text | ❌ | "" |
| 140 | `input_140` | `input_98_140` | Total Loan Amount | text | ❌ | "" |
| **Coverage Selection** |
| 83 | `input_83` | `choice_98_83_*` | Coverage Type | radio | ❌ | Essential/Beyond Essential/Industry Best |
| 13 | `input_13` | `choice_98_13_*` | Contract Term | radio | ❌ | 12/24/36/48/60/72/84/96/108/120 |
| 15 | `input_15` | Multiple IDs | Deductible | radio | ❌ | Multiple options |
| **Hidden Base Price Fields (447-456)** |
| 447 | `input_447` | `input_98_447` | 12 base | text | ✅ | "" |
| 448 | `input_448` | `input_98_448` | 24 base | text | ✅ | "" |
| 449 | `input_449` | `input_98_449` | 36 base | text | ✅ | "" |
| 450 | `input_450` | `input_98_450` | 48 base | text | ✅ | "" |
| 451 | `input_451` | `input_98_451` | 60 base | text | ✅ | "" |
| 452 | `input_452` | `input_98_452` | 72 base | text | ✅ | "" |
| 453 | `input_453` | `input_98_453` | 84 base | text | ✅ | "" |
| 454 | `input_454` | `input_98_454` | 96 base | text | ✅ | "" |
| 455 | `input_455` | `input_98_455` | 108 base | text | ✅ | "" |
| 456 | `input_456` | `input_98_456` | 120 base | text | ✅ | "" |

### **🔄 Fields with Same Names but Different IDs**

| **Input Name** | **Multiple IDs** | **Context** | **Issue** |
|----------------|------------------|-------------|-----------|
| `input_413` | `choice_98_413_0`, `choice_98_413_1`, `choice_98_413_2`, `choice_98_413_3` | Order Method Radio Group | ✅ **Normal** - Radio group |
| `input_137` | `choice_98_137_0`, `choice_98_137_1`, `choice_98_137_2`, `choice_98_137_3` | Order Method Confirmation | ✅ **Normal** - Radio group |
| `input_83` | `choice_98_83_0`, `choice_98_83_1`, `choice_98_83_2` | Coverage Type Radio | ✅ **Normal** - Radio group |
| `input_13` | `choice_98_13_0` through `choice_98_13_9` | Contract Term Radio | ✅ **Normal** - Radio group |
| `input_133` | `input_98_133` | Total Loan Amount | ⚠️ **Duplicate** - Same field appears twice |
| `input_140` | `input_98_140` | Total Loan Amount | ⚠️ **Duplicate** - Same field appears twice |

### **🔒 Hidden Input Fields**

| **Field ID** | **Input Name** | **Purpose** | **Value** |
|--------------|----------------|-------------|-----------|
| **Vehicle Data (Hidden)** |
| 11 | `input_11` | Series/Body Style | "ASA44L/ASA42L/AVA44L" |
| 8 | `input_8` | Engine | "2AR-FE" |
| 9 | `input_9` | Transmission/Cylinders | "6" |
| 10 | `input_10` | Drive Type | "4WD/4-Wheel Drive/4x4" |
| 53 | `input_53` | Hidden Odometer Copy | "" |
| 37 | `input_37` | Vehicle Class | "Class 1" |
| **System Fields (Hidden)** |
| 411 | `input_411` | Favorite Ordering Method | "Generating a Buyers Order, Entering Loan Amount Only, VSC Details Only, Recommend a Contract" |
| 414 | `input_414` | Continue By Indicator | "" |
| **Base Pricing (Hidden 447-456)** |
| 447-456 | `input_447` - `input_456` | Base prices for different terms | "" |
| **Base Pricing Beyond (Hidden 458-467)** |
| 458-467 | `input_458` - `input_467` | Beyond Essential base prices | "" |
| **Base Pricing Industry (Hidden 469-478)** |
| 469-478 | `input_469` - `input_478` | Industry Best base prices | "" |
| **Calculated Fields (Hidden)** |
| 229 | `input_229` | Contract Term in Months (hidden 1) | "" |
| 265 | `input_265` | Deductible (hidden) | "" |
| 281 | `input_281` | Final Contract Price (hidden) | "" |
| 233 | `input_233` | Limit of Liability (hidden) | "" |
| 317 | `input_317` | Contract Term in Months (hidden 2) | "" |
| 342 | `input_342` | Adjusted Limit of Liability 2 | "" |
| 402 | `input_402` | Markup Record | "" |
| **Form System Fields** |
| - | `gform_field_values` | Form field values | "" |
| - | `_gform_submit_nonce_98` | Security nonce | "337242c641" |
| - | `_wp_http_referer` | HTTP referer | "/ordercontract/" |
| - | `gform_submission_method` | Submission method | "postback" |
| - | `gform_theme` | Form theme | "gravity-theme" |
| - | `gform_style_settings` | Style settings | "" |
| - | `is_submit_98` | Submit flag | "1" |
| - | `gform_submit` | Form submit | "98" |
| - | `gform_save` | Form save | "" |
| - | `gform_resume_token` | Resume token | "" |
| - | `gform_unique_id` | Unique ID | "" |
| - | `state_98` | Form state | Long encoded string |
| - | `gform_target_page_number_98` | Target page | "2" |
| - | `gform_source_page_number_98` | Source page | "1" |
| - | `gform_uploaded_files` | Uploaded files | "" |

### **⚠️ Potential Issues Identified**

1. **Duplicate Field Names**: `input_133` and `input_140` both labeled "Total Loan Amount"
2. **Missing React Implementation**: Many hidden calculation fields (447-478, 458-467, 469-478)
3. **Complex Radio Groups**: Multiple radio groups with same name but different IDs
4. **System Fields**: WordPress/Gravity Forms specific hidden fields not in React app

### **✅ React App Sync Status**

| **Category** | **Total Fields** | **Implemented** | **Missing** | **Status** |
|--------------|------------------|-----------------|-------------|------------|
| **User/Demo** | 3 | 3 | 0 | ✅ **Complete** |
| **Vehicle Info** | 6 | 6 | 0 | ✅ **Complete** |
| **Hidden Vehicle** | 6 | 5 | 1 | ⚠️ **Missing input_53** |
| **Pricing** | 12 | 12 | 0 | ✅ **Complete** |
| **Coverage** | 3 | 2 | 1 | ⚠️ **Missing input_15** |
| **Hidden Calculations** | 50+ | 0 | 50+ | ❌ **Not Implemented** |
| **System Fields** | 15+ | 0 | 15+ | ❌ **WordPress Specific** |

**Total Visible Fields**: 30 ✅ **Implemented**  
**Total Hidden Fields**: 70+ ⚠️ **Partially Implemented**
