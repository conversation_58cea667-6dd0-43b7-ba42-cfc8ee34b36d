.orderFormContainer {
  max-width: 980px;
  margin: 2rem auto;
  font-family: 'Inter', 'Segoe UI', 'system-ui', 'Arial', sans-serif;
  padding: 2.5rem 2rem;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  background: #fff;
  border: 1px solid #e5e7eb;
}

.orderSectionHeader {
  font-weight: 800;
  font-size: 1.1rem;
  color: #1e293b;
  margin-bottom: 8px;
  letter-spacing: -0.01em;
}

.orderDivider {
  height: 1px;
  background: #e5e7eb;
  margin: 18px 0 24px 0;
  border: none;
}

.orderInputGroup {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.orderLabel {
  font-weight: 700;
  color: #222;
  margin-bottom: 2px;
  font-size: 1rem;
}

.orderInput {
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 7px 8px;
  font-size: 15px;
  background: #fff;
  transition: border 0.13s;
}
.orderInput:focus {
  border: 1.2px solid #2563eb;
  outline: none;
}

.orderGrid4 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 18px;
}
.orderGrid2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 18px;
}
.orderGrid3 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 18px;
}
.orderGridAddress {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 18px;
}

.orderButtonPrimary {
  font-size: 15px;
  padding: 0.6rem 1.3rem;
  border: 1px solid #2563eb;
  border-radius: 6px;
  background: #2563eb;
  color: #fff;
  font-weight: 700;
  cursor: pointer;
  transition: background 0.13s, border 0.13s;
}
.orderButtonPrimary:hover {
  background: #1d4ed8;
  border-color: #1d4ed8;
}
.orderButtonSecondary {
  font-size: 15px;
  padding: 0.6rem 1.3rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #f3f4f6;
  color: #334155;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.13s, border 0.13s;
}
.orderButtonSecondary:hover {
  background: #e5e7eb;
}

.shared-input-label {
  font-size: 13px;
  color: #64748b !important;
  font-weight: 500;
  margin-bottom: 2px;
}

.shared-input {
  color: #334155 !important;
  background: #fff;
}

.orderFormContainer label,
.orderFormContainer .shared-input-label {
  color: #64748b !important;
  background: transparent !important;
}

.orderFormContainer input,
.orderFormContainer select,
.orderFormContainer .shared-input {
  color: #334155 !important;
  background: #fff !important;
}

.orderFormContainer .shared-input-label {
  color: #64748b !important;
}

.orderSummaryPageBg {
  background: #f8fafc;
  min-height: 100vh;
  padding: 2.5rem 0;
}

.orderSectionCard {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(30,41,59,0.07);
  border: 1px solid #e5e7eb;
  margin-bottom: 2.2rem;
  padding: 2.1rem 2rem 1.7rem 2rem;
  position: relative;
}

.orderSectionAccent {
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 5px;
  background: linear-gradient(90deg, #2563eb 0%, #60a5fa 100%);
  border-radius: 14px 14px 0 0;
}

.orderSectionHeaderModern {
  font-size: 1.22rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 10px;
  letter-spacing: -0.01em;
  display: flex;
  align-items: center;
  gap: 10px;
}

.orderSectionDivider {
  height: 1.5px;
  background: #e5e7eb;
  margin: 12px 0 18px 0;
  border: none;
  border-radius: 1px;
}

.orderHelperText {
  font-size: 12px;
  color: #64748b;
  margin-top: 2px;
}

.signatureBox {
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  min-height: 90px;
  background: #f9fafb;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  font-size: 15px;
  position: relative;
}
.signatureBox .penIcon {
  position: absolute;
  bottom: 8px;
  right: 12px;
  opacity: 0.5;
}

.orderButtonPrimaryModern {
  font-size: 15px;
  padding: 0.6rem 1.3rem;
  border: none;
  border-radius: 7px;
  background: linear-gradient(90deg, #2563eb 0%, #60a5fa 100%);
  color: #fff;
  font-weight: 700;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(37,99,235,0.07);
  transition: background 0.13s, box-shadow 0.13s;
}
.orderButtonPrimaryModern:hover {
  background: #dc2626;
}
.orderButtonDangerModern {
  font-size: 15px;
  padding: 0.6rem 1.3rem;
  border: none;
  border-radius: 7px;
  background: linear-gradient(90deg, #dc2626 0%, #f87171 100%);
  color: #fff;
  font-weight: 700;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(220,38,38,0.07);
  transition: background 0.13s, box-shadow 0.13s;
}
.orderButtonDangerModern:hover {
  background: #b91c1c;
}

.orderButtonOutlinedRedModern {
  background: transparent;
  color: rgb(226,38,38);
  border: 2px solid rgb(226,38,38);
  font-weight: 600;
  border-radius: 6px;
  box-shadow: none;
  min-width: 140px;
  padding: 0.5rem 1.2rem;
  transition: background 0.18s, color 0.18s, border 0.18s;
  cursor: pointer;
}
.orderButtonOutlinedRedModern:hover,
.orderButtonOutlinedRedModern:focus {
  background: #fef2f2;
  color: rgb(226,38,38);
  border: 2px solid rgb(226,38,38);
  outline: none;
}

@media (max-width: 900px) {
  .orderSectionCard {
    padding: 1.2rem 0.7rem 1.2rem 0.7rem;
  }
}
@media (max-width: 700px) {
  .orderGrid4,
  .orderGrid3,
  .orderGrid2,
  .orderGridAddress {
    grid-template-columns: 1fr !important;
    gap: 8px !important;
  }
  .orderFormContainer {
    padding: 1rem 0.5rem !important;
    border-radius: 0 !important;
    width: 100vw !important;
    max-width: 100vw !important;
    margin: 0 !important;
    box-sizing: border-box !important;
  }
  .orderSectionCard {
    padding: 1rem 0.5rem !important;
    border-radius: 0 !important;
  }
  .signatureBox {
    min-height: 70px;
    font-size: 13px;
  }
}

.custom-radio, .custom-checkbox {
  width: 22px;
  height: 22px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-right: 8px;
  cursor: pointer;
  vertical-align: middle;
}

.lienholder-icon {
  width: 18px !important;
  height: 18px !important;
  display: inline-block;
  vertical-align: middle;
}

.lienholder-option {
  color: #334155 !important;
  background: #fff !important;
  border: 1.5px solid #d1d5db !important;
  border-radius: 6px;
  padding: 3px 8px;
  margin-bottom: 7px;
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
  min-height: 20px;
  cursor: pointer;
  transition: border 0.13s, background 0.13s;
}
.lienholder-option.selected {
  border: 1.7px solid #dc2626 !important;
  background: #f0f7ff !important;
}
.lienholder-option:hover, .lienholder-option:focus-within {
  border: 1.5px solid #dc2626 !important;
  background: #f3f6fa !important;
}

.lienholder-option input[type=radio],
.lienholder-option input[type=checkbox] {
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
}

.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

input[type=radio].visually-hidden,
input[type=checkbox].visually-hidden {
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
}

.lienholder-tab-bar {
  display: flex;
  gap: 8px;
  background: #f3f6fa;
  border-radius: 8px;
  padding: 0.2rem 0.2rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 1px 4px rgba(30,41,59,0.04);
  border: 1px solid #e5e7eb;
  flex-wrap: wrap;
}
.lienholder-tab {
  border: none;
  outline: none;
  background: transparent;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  border-radius: 999px;
  padding: 0.5rem 1.2rem;
  cursor: pointer;
  transition: background 0.13s, color 0.13s, box-shadow 0.13s;
  margin-bottom: 0;
  position: relative;
  min-width: 0;
  box-shadow: none;
}
.lienholder-tab:hover, .lienholder-tab:focus {
  background: #e0e7ef;
  color: #dc2626;
}
.lienholder-tab.active {
  background: #fff;
  color: #dc2626;
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(37,99,235,0.07);
  border: 1.5px solid #dc2626;
  z-index: 1;
}
@media (max-width: 600px) {
  .lienholder-tab-bar {
    flex-wrap: wrap;
    gap: 4px;
    padding: 0.15rem 0.1rem;
  }
  .lienholder-tab {
    font-size: 13px;
    padding: 0.4rem 0.7rem;
  }
}

.tabFade {
  opacity: 0;
  animation: tabFadeIn 0.32s cubic-bezier(0.4,0,0.2,1) forwards;
}
@keyframes tabFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.customCheckbox {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border: 2px solid #cbd5e1;
  border-radius: 5px;
  background: #fff;
  transition: border 0.18s, background 0.18s;
  margin-right: 7px;
  box-sizing: border-box;
  position: relative;
}
.customCheckbox.checked {
  background: #f1f5f9;
  border-color: #64748b;
}
.customCheckbox svg {
  display: block;
  width: 13px;
  height: 13px;
  color: #64748b;
}
.visually-hidden-checkbox {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  pointer-events: none;
} 