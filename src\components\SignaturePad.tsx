import React, { useRef, useEffect, useState } from 'react';

interface SignaturePadProps {
  value?: string;
  onChange?: (signatureData: string) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const SignaturePad: React.FC<SignaturePadProps> = ({
  value,
  onChange,
  placeholder = "Sign here",
  required = false,
  disabled = false,
  className = "",
  style = {}
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [hasSignature, setHasSignature] = useState(false);
  const [context, setContext] = useState<CanvasRenderingContext2D | null>(null);
  const [isValid, setIsValid] = useState(false);

  // Function to validate signature quality
  const validateSignature = (canvas: HTMLCanvasElement): boolean => {
    const ctx = canvas.getContext('2d');
    if (!ctx) return false;

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    // Count non-transparent pixels (signature content)
    let signaturePixels = 0;
    for (let i = 3; i < data.length; i += 4) {
      if (data[i] > 0) { // Alpha channel > 0 means pixel has content
        signaturePixels++;
      }
    }

    // Calculate percentage of canvas covered by signature
    const totalPixels = canvas.width * canvas.height;
    const coveragePercentage = (signaturePixels / totalPixels) * 100;

    // Minimum requirements: at least 0.5% coverage and at least 100 pixels
    const hasMinimumCoverage = coveragePercentage >= 0.5;
    const hasMinimumPixels = signaturePixels >= 100;

    return hasMinimumCoverage && hasMinimumPixels;
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    // Configure drawing context
    ctx.strokeStyle = '#334155';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    setContext(ctx);

    // Load existing signature if provided
    if (value) {
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, 0, 0);
        const valid = validateSignature(canvas);
        setHasSignature(true);
        setIsValid(valid);
      };
      img.src = value;
    }
  }, [value]);

  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (disabled || !context) return;
    
    setIsDrawing(true);
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = (e as any).touches ? (e as any).touches[0].clientX - rect.left : (e as any).clientX - rect.left;
    const y = (e as any).touches ? (e as any).touches[0].clientY - rect.top : (e as any).clientY - rect.top;

    context.beginPath();
    context.moveTo(x, y);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing || disabled || !context) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = (e as any).touches ? (e as any).touches[0].clientX - rect.left : (e as any).clientX - rect.left;
    const y = (e as any).touches ? (e as any).touches[0].clientY - rect.top : (e as any).clientY - rect.top;

    context.lineTo(x, y);
    context.stroke();
  };

  const stopDrawing = () => {
    if (!isDrawing || disabled) return;
    
    setIsDrawing(false);
    
    // Convert canvas to data URL and call onChange
    const canvas = canvasRef.current;
    if (canvas && onChange) {
      const signatureData = canvas.toDataURL();
      const valid = validateSignature(canvas);
      
      setHasSignature(true);
      setIsValid(valid);
      
      // Only call onChange if signature is valid or if we're clearing it
      if (valid || signatureData === '') {
        onChange(signatureData);
      }
    }
  };

  const clearSignature = () => {
    if (disabled || !context || !canvasRef.current) return;
    
    const canvas = canvasRef.current;
    context.clearRect(0, 0, canvas.width, canvas.height);
    setHasSignature(false);
    setIsValid(false);
    
    if (onChange) {
      onChange('');
    }
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    e.preventDefault();
    startDrawing(e);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    e.preventDefault();
    draw(e);
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLCanvasElement>) => {
    e.preventDefault();
    stopDrawing();
  };

  const handleTouchStart = (e: React.TouchEvent<HTMLCanvasElement>) => {
    e.preventDefault();
    startDrawing(e);
  };

  const handleTouchMove = (e: React.TouchEvent<HTMLCanvasElement>) => {
    e.preventDefault();
    draw(e);
  };

  const handleTouchEnd = (e: React.TouchEvent<HTMLCanvasElement>) => {
    e.preventDefault();
    stopDrawing();
  };

  return (
    <div 
      className={`signature-pad ${className}`}
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: 8,
        ...style
      }}
    >
      <div
        style={{
          position: 'relative',
          border: hasSignature ? '2px solid #16a34a' : '2px dashed #cbd5e1',
          borderRadius: 8,
          background: '#fff',
          cursor: disabled ? 'not-allowed' : 'crosshair',
          transition: 'border-color 0.2s ease'
        }}
      >
        <canvas
          ref={canvasRef}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={stopDrawing}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          style={{
            width: '100%',
            height: 120,
            display: 'block',
            borderRadius: 6
          }}
        />
        {!hasSignature && (
          <div
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              color: '#64748b',
              fontSize: 14,
              fontWeight: 500,
              pointerEvents: 'none',
              display: 'flex',
              alignItems: 'center',
              gap: 6
            }}
          >
            <span role="img" aria-label="pen">🖊️</span>
            {placeholder}
          </div>
        )}
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <button
            type="button"
            onClick={clearSignature}
            disabled={disabled || !hasSignature}
            style={{
              background: 'transparent',
              border: '1px solid #e5e7eb',
              borderRadius: 4,
              padding: '4px 8px',
              fontSize: 12,
              color: hasSignature ? '#64748b' : '#cbd5e1',
              cursor: (disabled || !hasSignature) ? 'not-allowed' : 'pointer',
              transition: 'all 0.2s ease'
            }}
          >
            Clear
          </button>
          {window.innerWidth > 800 && (
            <div>
              {required && !hasSignature && (
                <span style={{ fontSize: 12, color: '#b91c1c', fontWeight: 500 }}>
                  Signature required
                </span>
              )}
              {hasSignature && !isValid && (
                <span style={{ fontSize: 12, color: '#b91c1c', fontWeight: 500 }}>
                  Please provide a more complete signature
                </span>
              )}
              {hasSignature && isValid && (
                <span style={{ fontSize: 12, color: '#16a34a', fontWeight: 500 }}>
                  ✓ Valid signature
                </span>
              )}
            </div>
          )}
        </div>
        {window.innerWidth <= 800 && (
          <div style={{ textAlign: 'left' }}>
            {required && !hasSignature && (
              <span style={{ fontSize: 12, color: '#b91c1c', fontWeight: 500 }}>
                Signature required
              </span>
            )}
            {hasSignature && !isValid && (
              <span style={{ fontSize: 12, color: '#b91c1c', fontWeight: 500 }}>
                Please provide a more complete signature
              </span>
            )}
            {hasSignature && isValid && (
              <span style={{ fontSize: 12, color: '#16a34a', fontWeight: 500 }}>
                ✓ Valid signature
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SignaturePad; 
