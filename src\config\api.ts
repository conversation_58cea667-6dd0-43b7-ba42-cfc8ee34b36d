import { ENVIRONMENT } from './environment';

// API Configuration
export const API_CONFIG = {
  // Base URL for the API - can be overridden by environment variables
  BASE_URL: ENVIRONMENT.API_BASE_URL,
  
  // API endpoints
  ENDPOINTS: {
    VIN_DECODE: '/vin/decode',
  },
  
  // Request timeout in milliseconds
  TIMEOUT: 10000,
  
  // Retry configuration
  RETRY: {
    MAX_ATTEMPTS: 3,
    DELAY: 1000, // milliseconds
  },
} as const;

// Helper function to build full API URLs
export function buildApiUrl(endpoint: string): string {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
}

// Environment-specific configurations
export const ENV_CONFIG = {
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
  isTest: import.meta.env.MODE === 'test',
} as const; 