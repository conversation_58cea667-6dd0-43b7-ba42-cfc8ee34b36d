import { useState, useCallback, useEffect } from 'react';

export interface StepStatus {
  [stepId: string]: {
    completed: boolean;
    current: boolean;
    accessible: boolean;
  };
}

export interface Step {
  id: string;
  label: string;
  description: string;
  completed: boolean;
  current: boolean;
  onClick?: () => void;
}

interface UseStepBarProps {
  steps: Step[];
  initialStep?: string;
  allowBackwardNavigation?: boolean;
}

export const useStepBar = ({ 
  steps, 
  initialStep, 
  allowBackwardNavigation = true 
}: UseStepBarProps) => {
  const [currentStepId, setCurrentStepId] = useState(initialStep || steps[0]?.id || '');
  const [stepStatus, setStepStatus] = useState<StepStatus>({});

  // Initialize step status
  useEffect(() => {
    const initialStatus: StepStatus = {};
    steps.forEach((step, index) => {
      initialStatus[step.id] = {
        completed: step.completed || false,
        current: step.id === currentStepId,
        accessible: allowBackwardNavigation || index <= steps.findIndex(s => s.id === currentStepId)
      };
    });
    setStepStatus(initialStatus);
  }, [steps, currentStepId, allowBackwardNavigation]);

  // Update current step
  const setCurrentStep = useCallback((stepId: string) => {
    setCurrentStepId(stepId);
    setStepStatus(prev => {
      const newStatus = { ...prev };
      // Update current step
      Object.keys(newStatus).forEach(id => {
        newStatus[id] = {
          ...newStatus[id],
          current: id === stepId
        };
      });
      return newStatus;
    });
  }, []);

  // Mark step as completed
  const markStepCompleted = useCallback((stepId: string, completed: boolean = true) => {
    setStepStatus(prev => ({
      ...prev,
      [stepId]: {
        ...prev[stepId],
        completed
      }
    }));
  }, []);

  // Mark step as accessible
  const setStepAccessible = useCallback((stepId: string, accessible: boolean) => {
    setStepStatus(prev => ({
      ...prev,
      [stepId]: {
        ...prev[stepId],
        accessible
      }
    }));
  }, []);

  // Navigate to next step
  const goToNextStep = useCallback(() => {
    const currentIndex = steps.findIndex(step => step.id === currentStepId);
    if (currentIndex < steps.length - 1) {
      const nextStep = steps[currentIndex + 1];
      setCurrentStep(nextStep.id);
      // Mark current step as completed when moving to next
      markStepCompleted(currentStepId, true);
    }
  }, [currentStepId, steps, setCurrentStep, markStepCompleted]);

  // Navigate to previous step
  const goToPreviousStep = useCallback(() => {
    const currentIndex = steps.findIndex(step => step.id === currentStepId);
    if (currentIndex > 0) {
      const prevStep = steps[currentIndex - 1];
      setCurrentStep(prevStep.id);
    }
  }, [currentStepId, steps, setCurrentStep]);

  // Navigate to specific step
  const goToStep = useCallback((stepId: string) => {
    const step = steps.find(s => s.id === stepId);
    if (step && stepStatus[stepId]?.accessible) {
      setCurrentStep(stepId);
    }
  }, [steps, stepStatus, setCurrentStep]);

  // Get current step info
  const getCurrentStep = useCallback(() => {
    return steps.find(step => step.id === currentStepId);
  }, [steps, currentStepId]);

  // Check if current step is the last step
  const isLastStep = useCallback(() => {
    const currentIndex = steps.findIndex(step => step.id === currentStepId);
    return currentIndex === steps.length - 1;
  }, [steps, currentStepId]);

  // Check if current step is the first step
  const isFirstStep = useCallback(() => {
    const currentIndex = steps.findIndex(step => step.id === currentStepId);
    return currentIndex === 0;
  }, [steps, currentStepId]);

  // Get progress percentage
  const getProgress = useCallback(() => {
    const completedSteps = Object.values(stepStatus).filter(status => status.completed).length;
    return (completedSteps / steps.length) * 100;
  }, [stepStatus, steps.length]);

  return {
    currentStepId,
    stepStatus,
    setCurrentStep,
    markStepCompleted,
    setStepAccessible,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    getCurrentStep,
    isLastStep,
    isFirstStep,
    getProgress
  };
}; 